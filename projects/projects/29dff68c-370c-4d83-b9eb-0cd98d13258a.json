{"project_id": "29dff68c-370c-4d83-b9eb-0cd98d13258a", "name": "Web Agent Page", "description": "Proyecto que engloba los test cases a ejecutar en la url https://web-agent-playground.lovable.app", "tags": ["web-agent-url"], "test_suites": {"52eb8e73-9b8b-4c36-aa79-9a71460b2f51": {"suite_id": "52eb8e73-9b8b-4c36-aa79-9a71460b2f51", "name": "Funcionalidad core", "description": "Suite de test cases que se encargará de realizar las pruebas mínimas e indispensables para que el sistema este utilizable", "tags": ["core", "test-cases"], "test_cases": {"790f99de-2eb6-4f92-8f87-8cc07a2b87d7": {"test_id": "790f99de-2eb6-4f92-8f87-8cc07a2b87d7", "name": "<PERSON><PERSON>", "description": "Login del usuario", "instrucciones": "Ingresar a la url solicitada y loguearse con el usuario:<EMAIL> y contraseña:admin123", "historia_de_usuario": "El usuario debe poder loguearse al ingresar credenciales validas", "gherkin": "Característica: Autenticación de Usuario\n\nEscenario: Inicio de sesión exitoso con credenciales válidas\n  Dado que el usuario está en la página de inicio de sesión\n  Cuando el usuario ingresa \"<EMAIL>\" como nombre de usuario y \"admin123\" como contraseña\n  Y el usuario intenta iniciar sesión\n  Entonces el usuario debería iniciar sesión exitosamente", "url": "https://web-agent-playground.lovable.app", "tags": [], "created_at": "2025-06-15T05:45:03.012265", "updated_at": "2025-06-15T05:54:45.232174", "history_files": ["tests/smoke_test_20250615055251/history.json"], "status": "Failed", "last_execution": "2025-06-15T05:54:45.232172", "code": "", "framework": ""}, "dae6b1e5-4f45-4973-bdd6-c71514fef1b3": {"test_id": "dae6b1e5-4f45-4973-bdd6-c71514fef1b3", "name": "<PERSON><PERSON><PERSON> usuario", "description": "Test case para poder crear un usuario", "instrucciones": "Ingresar a la url solicitada ya habiendote logueado. \nNavegar en el sidebar hasta la seccion de usuarios.\nHacer click en el boton de crear usuario y cargar un nombre y mail ficticios, y aceptar\nLuego ver si se cargó el nuevo registro de usuario al final de la tabla de usuarios\n", "historia_de_usuario": "El usuario debe poder crear un usuario nuevo", "gherkin": "", "url": "https://web-agent-playground.lovable.app", "tags": ["create-user", "test-case"], "created_at": "2025-06-15T05:48:59.299840", "updated_at": "2025-06-15T05:54:57.658777", "history_files": [], "status": "Failed", "last_execution": "2025-06-15T05:54:57.658774", "code": "", "framework": ""}, "67927947-b712-413a-a1ab-b851d10ff241": {"test_id": "67927947-b712-413a-a1ab-b851d10ff241", "name": "Cargar producto", "description": "Que el usuario pueda cargar un nuevo producto", "instrucciones": "Dentro de la url detallada navegar en el sidebar hasta la seccion de productos\nhacer click en el boton crear nuevo producto\ncargar información ficticia en todos los campos del formulario\nconfirmar que el producto se haya agregado a la tabla de productos (al final de ella)", "historia_de_usuario": "Que el usuario pueda cargar un nuevo producto", "gherkin": "", "url": "https://web-agent-playground.lovable.app", "tags": ["create-product"], "created_at": "2025-06-15T05:51:55.760717", "updated_at": "2025-06-15T05:55:14.618170", "history_files": [], "status": "Failed", "last_execution": "2025-06-15T05:55:14.618167", "code": "", "framework": ""}}, "created_at": "2025-06-15T05:41:12.016193", "updated_at": "2025-06-15T05:51:55.760721"}}, "created_at": "2025-06-15T05:39:43.143426", "updated_at": "2025-06-15T05:41:12.016197", "github_config": {"enabled": false, "repo": null, "token": null}}