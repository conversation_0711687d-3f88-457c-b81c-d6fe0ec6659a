{"project_id": "c9042f7a-f9f0-42ae-ab5b-9a5cd26467ec", "name": "Test Github3", "description": "<PERSON><PERSON><PERSON>das<PERSON>", "tags": [], "test_suites": {"f4cebe3e-7505-4cf9-ad5e-efc8bb906bff": {"suite_id": "f4cebe3e-7505-4cf9-ad5e-efc8bb906bff", "name": "GitHub PR Analysis", "description": "Tests generated from GitHub Pull Request analysis", "tags": ["github-pr-analysis", "automated"], "test_cases": {}, "created_at": "2025-06-14T19:13:22.198520", "updated_at": "2025-06-14T19:13:22.198520"}}, "created_at": "2025-06-14T19:13:22.198090", "updated_at": "2025-06-14T19:13:22.198524", "github_config": {"enabled": true, "repo": "na<PERSON><PERSON><PERSON>/qak", "token": "****************************************"}}