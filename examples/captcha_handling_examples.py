"""
Ejemplo completo de uso del sistema de manejo de CAPTCHAs en AgentQA
"""

import asyncio
import os
from src.Utilities.captcha_helper import (
    execute_with_captcha_handling,
    get_captcha_config,
    CaptchaConfig
)
from src.Utilities.browser_helper import controller
from src.Config.browser_config import BrowserConfigurations

async def example_google_search_with_captcha():
    """Ejemplo: Búsqueda en Google que puede encontrar CAPTCHAs"""
    
    scenario = """
    Given I am on the Google search page
    When I search for "automated testing tools"
    And I click on the first search result
    Then I should see content related to testing tools
    """
    
    # Configuración especializada para Google
    config = get_captcha_config(
        site_type='google_search',
        headless=False,  # Importante: modo visible para CAPTCHAs
        wait_between_actions=3.0,
        max_steps=30
    )
    
    try:
        result = await execute_with_captcha_handling(
            scenario=scenario,
            controller_instance=controller,
            api_key=os.environ.get("GOOGLE_API_KEY"),
            url="https://www.google.com",
            max_retries=3
        )
        
        print("✅ Ejecución completada exitosamente")
        print(f"Final result: {result.final_result()}")
        
    except Exception as e:
        print(f"❌ Error en ejecución: {e}")


async def example_with_proxy_rotation():
    """Ejemplo: Uso con rotación de proxy para evitar CAPTCHAs"""
    
    scenario = """
    Given I navigate to a site that frequently shows CAPTCHAs
    When I perform multiple searches
    Then I should be able to complete the task
    """
    
    # Configurar proxy
    proxy_config = {
        "server": "http://proxy.example.com:8080",
        "username": "user",
        "password": "pass"
    }
    
    config = CaptchaConfig(
        stealth=True,
        headless=False,
        proxy=proxy_config,
        wait_between_actions=4.0,
        allowed_domains=['*.example.com', '*.recaptcha.net']
    )
    
    try:
        result = await execute_with_captcha_handling(
            scenario=scenario,
            controller_instance=controller,
            api_key=os.environ.get("GOOGLE_API_KEY"),
            proxy_config=proxy_config,
            max_retries=5  # Más reintentos con proxy
        )
        
        print("✅ Ejecución con proxy completada")
        
    except Exception as e:
        print(f"❌ Error con proxy: {e}")


async def example_using_browser_configurations():
    """Ejemplo: Uso con configuraciones predefinidas de AgentQA"""
    
    scenario = """
    Given I am on a website with CAPTCHA protection
    When I try to access protected content
    Then I should successfully bypass the CAPTCHA and access the content
    """
    
    # Usar configuración predefinida
    config = BrowserConfigurations.get_captcha_config(
        # Sobrescribir algunos valores si es necesario
        max_steps=50,
        wait_between_actions=2.5
    )
    
    try:
        result = await execute_with_captcha_handling(
            scenario=scenario,
            controller_instance=controller,
            api_key=os.environ.get("GOOGLE_API_KEY"),
            config=config  # Usar configuración predefinida
        )
        
        print("✅ Ejecución con configuración predefinida completada")
        
    except Exception as e:
        print(f"❌ Error: {e}")


def run_examples():
    """Ejecutar todos los ejemplos"""
    print("🚀 Iniciando ejemplos de manejo de CAPTCHA...")
    
    # Ejecutar ejemplos uno por uno
    asyncio.run(example_google_search_with_captcha())
    print("\n" + "="*50 + "\n")
    
    asyncio.run(example_with_proxy_rotation())
    print("\n" + "="*50 + "\n")
    
    asyncio.run(example_using_browser_configurations())
    
    print("✅ Todos los ejemplos completados")


if __name__ == "__main__":
    # Verificar que tenemos las API keys necesarias
    if not os.environ.get("GOOGLE_API_KEY"):
        print("❌ Error: GOOGLE_API_KEY no encontrada en variables de entorno")
        exit(1)
    
    run_examples()
