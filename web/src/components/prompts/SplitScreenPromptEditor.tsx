// Split Screen Prompt Editor Component
"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { 
  Languages, 
  ArrowRight, 
  Save, 
  RefreshCw,
  Eye,
  Edit3
} from "lucide-react";

interface SplitScreenEditorProps {
  initialSpanishText?: string;
  onSave?: (spanishText: string, englishText: string) => void;
  promptType?: string;
}

export function SplitScreenPromptEditor({ 
  initialSpanishText = "", 
  onSave,
  promptType = "general"
}: SplitScreenEditorProps) {
  const [spanishText, setSpanishText] = useState(initialSpanishText);
  const [englishText, setEnglishText] = useState("");
  const [isTranslating, setIsTranslating] = useState(false);
  const [autoTranslate, setAutoTranslate] = useState(true);
  const { toast } = useToast();

  const translateToEnglish = async (text: string) => {
    if (!text.trim()) {
      setEnglishText("");
      return;
    }

    setIsTranslating(true);
    try {
      const response = await fetch('/api/translate-prompt', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'ngrok-skip-browser-warning': 'true'
        },
        body: JSON.stringify({
          text,
          source: 'es',
          target: 'en',
          type: 'prompt'
        })
      });

      if (!response.ok) {
        throw new Error('Translation failed');
      }

      const result = await response.json();
      setEnglishText(result.translatedText);
    } catch (error) {
      console.error('Translation error:', error);
      toast({
        title: "Translation Error",
        description: "Failed to translate prompt. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsTranslating(false);
    }
  };

  // Auto-translate when Spanish text changes
  useEffect(() => {
    if (!autoTranslate) return;
    
    const debounceTimer = setTimeout(() => {
      translateToEnglish(spanishText);
    }, 1000); // 1 second delay

    return () => clearTimeout(debounceTimer);
  }, [spanishText, autoTranslate]);

  const handleSave = () => {
    if (onSave) {
      onSave(spanishText, englishText);
    }
    toast({
      title: "Prompt Saved",
      description: "Your prompt has been saved successfully."
    });
  };

  const handleManualTranslate = () => {
    translateToEnglish(spanishText);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Languages className="h-5 w-5" />
          <h2 className="text-xl font-semibold">Split Screen Prompt Editor</h2>
          <Badge variant="secondary">{promptType}</Badge>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoTranslate(!autoTranslate)}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${autoTranslate ? 'animate-pulse' : ''}`} />
            Auto-Translate: {autoTranslate ? 'ON' : 'OFF'}
          </Button>
          <Button onClick={handleSave}>
            <Save className="h-4 w-4 mr-2" />
            Save Prompt
          </Button>
        </div>
      </div>

      {/* Split Screen Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Spanish Input */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Edit3 className="h-4 w-4" />
              <span>Spanish Input</span>
              <Badge>🇪🇸 ES</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="spanish-input">Write your prompt in Spanish</Label>
              <Textarea
                id="spanish-input"
                placeholder="Escribe tu prompt en español aquí..."
                value={spanishText}
                onChange={(e) => setSpanishText(e.target.value)}
                className="min-h-[400px] font-mono text-sm"
              />
            </div>
            
            {!autoTranslate && (
              <Button
                variant="outline"
                onClick={handleManualTranslate}
                disabled={isTranslating}
                className="w-full"
              >
                {isTranslating ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <ArrowRight className="h-4 w-4 mr-2" />
                )}
                Translate to English
              </Button>
            )}
          </CardContent>
        </Card>

        {/* English Output */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Eye className="h-4 w-4" />
              <span>English Translation</span>
              <Badge>🇺🇸 EN</Badge>
              {isTranslating && (
                <Badge variant="secondary" className="animate-pulse">
                  Translating...
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div>
              <Label htmlFor="english-output">Auto-generated English prompt</Label>
              <Textarea
                id="english-output"
                placeholder="English translation will appear here..."
                value={englishText}
                onChange={(e) => setEnglishText(e.target.value)}
                className="min-h-[400px] font-mono text-sm bg-muted/50"
              />
              <p className="text-xs text-muted-foreground mt-2">
                💡 This is the actual prompt that will be used by the system. You can edit it if needed.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Info Section */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            <Languages className="h-5 w-5 text-blue-600 mt-0.5" />
            <div className="space-y-2">
              <h3 className="font-semibold text-blue-800">How it works:</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• <strong>Write naturally in Spanish</strong> - express your ideas clearly</li>
                <li>• <strong>Auto-translation to English</strong> - maintains technical precision</li>
                <li>• <strong>System uses English prompts</strong> - for consistency and accuracy</li>
                <li>• <strong>Responses in your language</strong> - based on user settings</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
