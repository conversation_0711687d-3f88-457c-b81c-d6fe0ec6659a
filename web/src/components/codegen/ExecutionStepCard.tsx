"use client";

import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { CheckCircle2, XCircle, ChevronDown, ChevronRight, Clock, AlertTriangle, FileText } from 'lucide-react';
import type { CodegenExecutionStep } from '@/lib/types';

interface ExecutionStepCardProps {
  step: CodegenExecutionStep;
  index: number;
}

export function ExecutionStepCard({ step, index }: ExecutionStepCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const getStatusIcon = () => {
    switch (step.status) {
      case 'completed':
        return <CheckCircle2 className="h-5 w-5 text-emerald-600 drop-shadow-sm" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-rose-600 drop-shadow-sm" />;
      default:
        return <Clock className="h-5 w-5 text-sky-600 drop-shadow-sm" />;
    }
  };

  const getStatusColor = () => {
    switch (step.status) {
      case 'completed':
        return 'border-emerald-200/60 bg-gradient-to-br from-emerald-50/40 to-green-50/60 hover:from-emerald-50/60 hover:to-green-50/80 hover:shadow-lg hover:shadow-emerald-100/30';
      case 'error':
        return 'border-rose-200/60 bg-gradient-to-br from-rose-50/40 to-red-50/60 hover:from-rose-50/60 hover:to-red-50/80 hover:shadow-lg hover:shadow-rose-100/30';
      default:
        return 'border-sky-200/60 bg-gradient-to-br from-sky-50/40 to-blue-50/60 hover:from-sky-50/60 hover:to-blue-50/80 hover:shadow-lg hover:shadow-sky-100/30';
    }
  };

  const getBadgeVariant = () => {
    switch (step.status) {
      case 'completed':
        return 'default' as const;
      case 'error':
        return 'destructive' as const;
      default:
        return 'secondary' as const;
    }
  };

  // Determinar si hay detalles para mostrar (descripción, timestamp, o detalles específicos)
  const hasDetails = Boolean(
    step.timestamp ||
    (step.description && step.description !== "No description available") ||
    (step.details && Object.keys(step.details).length > 0)
  );

  return (
    <Card className={`transition-all duration-200 hover:shadow-md overflow-hidden ${getStatusColor()}`}>
      <CardContent className="p-4">
        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleTrigger asChild>
            <Button
              variant="ghost"
              className="w-full justify-between p-0 h-auto hover:bg-transparent overflow-hidden"
            >
              <div className="flex items-start gap-3 text-left flex-1 min-w-0 overflow-hidden">
                <div className="flex-shrink-0 mt-0.5">
                  {getStatusIcon()}
                </div>
                <div className="flex-1 min-w-0 overflow-hidden">
                  <div className="flex items-center gap-2 mb-1 flex-wrap">
                    <span className="font-medium text-sm break-words">
                      Paso {step.step_number}: {step.action}
                    </span>
                    <Badge variant={getBadgeVariant()} className="text-xs flex-shrink-0">
                      {step.status === 'completed' ? 'Completado' :
                       step.status === 'error' ? 'Error' : 'En progreso'}
                    </Badge>
                  </div>
                  {/* Solo mostrar descripción básica si hay error, de lo contrario ocultar hasta expandir */}
                  {step.status === 'error' && step.description && step.description !== "No description available" && (
                    <p className="text-sm text-muted-foreground break-words overflow-hidden">
                      {step.description}
                    </p>
                  )}
                </div>
              </div>
              {hasDetails && (
                <div className="flex-shrink-0 ml-2">
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </div>
              )}
            </Button>
          </CollapsibleTrigger>

          {hasDetails && (
            <CollapsibleContent className="mt-3 pt-3 border-t border-border/50">
              <div className="space-y-3">
                <h4 className="text-sm font-medium flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  Detalles del paso
                </h4>

                {/* Timestamp */}
                {step.timestamp && (
                  <div className="p-4 bg-gradient-to-r from-slate-50/60 to-gray-50/60 border border-slate-200/60 rounded-lg shadow-sm">
                    <h5 className="text-sm font-semibold mb-2 text-slate-800 flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Ejecutado:
                    </h5>
                    <p className="text-sm text-slate-700 font-medium">
                      {new Date(step.timestamp).toLocaleString('es-ES', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: false
                      })}
                    </p>
                  </div>
                )}

                {/* Descripción completa */}
                {step.description && step.description !== "No description available" && (
                  <div className="p-4 bg-gradient-to-r from-indigo-50/60 to-purple-50/60 border border-indigo-200/60 rounded-lg shadow-sm">
                    <h5 className="text-sm font-semibold mb-2 text-indigo-800 flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Descripción:
                    </h5>
                    <p className="text-sm text-indigo-700 break-words whitespace-pre-wrap leading-relaxed">
                      {step.description}
                    </p>
                  </div>
                )}

                <div className="grid gap-4">
                  {step.details?.error && (
                    <div className="p-4 bg-gradient-to-r from-rose-50/60 to-red-50/60 border border-rose-200/60 rounded-lg shadow-sm">
                      <h5 className="text-sm font-semibold text-rose-800 mb-2 flex items-center gap-2">
                        <XCircle className="h-4 w-4" />
                        Error:
                      </h5>
                      <p className="text-sm text-rose-700 break-words whitespace-pre-wrap leading-relaxed">
                        {step.details.error}
                      </p>
                    </div>
                  )}

                  {step.details?.result && (
                    <div className="p-4 bg-gradient-to-r from-emerald-50/60 to-green-50/60 border border-emerald-200/60 rounded-lg shadow-sm">
                      <h5 className="text-sm font-semibold text-emerald-800 mb-2 flex items-center gap-2">
                        <CheckCircle2 className="h-4 w-4" />
                        Resultado:
                      </h5>
                      <p className="text-sm text-emerald-700 break-words whitespace-pre-wrap leading-relaxed">
                        {step.details.result}
                      </p>
                    </div>
                  )}

                  {step.details?.evaluation && step.details.evaluation !== "Unknown" && (
                    <div className="p-4 bg-gradient-to-r from-blue-50/60 to-cyan-50/60 border border-blue-200/60 rounded-lg shadow-sm">
                      <h5 className="text-sm font-semibold text-blue-800 mb-2 flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4" />
                        Evaluación:
                      </h5>
                      <p className="text-sm text-blue-700 break-words whitespace-pre-wrap leading-relaxed">
                        {step.details.evaluation}
                      </p>
                    </div>
                  )}

                  {step.details?.memory && (
                    <div className="p-4 bg-gradient-to-r from-violet-50/60 to-purple-50/60 border border-violet-200/60 rounded-lg shadow-sm">
                      <h5 className="text-sm font-semibold text-violet-800 mb-2 flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        Contexto:
                      </h5>
                      <p className="text-sm text-violet-700 break-words whitespace-pre-wrap leading-relaxed">
                        {step.details.memory}
                      </p>
                    </div>
                  )}

                  {step.details?.next_goal && (
                    <div className="p-4 bg-gradient-to-r from-teal-50/60 to-emerald-50/60 border border-teal-200/60 rounded-lg shadow-sm">
                      <h5 className="text-sm font-semibold text-teal-800 mb-2 flex items-center gap-2">
                        <ChevronRight className="h-4 w-4" />
                        Objetivo:
                      </h5>
                      <p className="text-sm text-teal-700 break-words whitespace-pre-wrap leading-relaxed">
                        {step.details.next_goal}
                      </p>
                    </div>
                  )}

                  {step.details?.error_type && (
                    <div className="p-4 bg-gradient-to-r from-amber-50/60 to-orange-50/60 border border-amber-200/60 rounded-lg shadow-sm">
                      <h5 className="text-sm font-semibold text-amber-800 mb-2 flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4" />
                        Tipo de error:
                      </h5>
                      <p className="text-sm text-amber-700 break-words leading-relaxed">
                        {step.details.error_type}
                      </p>
                      {step.details.error_message && (
                        <p className="text-sm text-amber-600 mt-2 break-words whitespace-pre-wrap leading-relaxed">
                          {step.details.error_message}
                        </p>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </CollapsibleContent>
          )}
        </Collapsible>
      </CardContent>
    </Card>
  );
}
