"use client";

import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { CheckCircle2, XCircle, ChevronDown, ChevronRight, Clock, AlertTriangle } from 'lucide-react';
import type { CodegenExecutionStep } from '@/lib/types';

interface ExecutionStepCardProps {
  step: CodegenExecutionStep;
  index: number;
}

export function ExecutionStepCard({ step, index }: ExecutionStepCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const getStatusIcon = () => {
    switch (step.status) {
      case 'completed':
        return <CheckCircle2 className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-blue-500" />;
    }
  };

  const getStatusColor = () => {
    switch (step.status) {
      case 'completed':
        return 'border-green-200 bg-green-50/50';
      case 'error':
        return 'border-red-200 bg-red-50/50';
      default:
        return 'border-blue-200 bg-blue-50/50';
    }
  };

  const getBadgeVariant = () => {
    switch (step.status) {
      case 'completed':
        return 'default' as const;
      case 'error':
        return 'destructive' as const;
      default:
        return 'secondary' as const;
    }
  };

  // Determinar si hay detalles para mostrar (descripción, timestamp, o detalles específicos)
  const hasDetails = Boolean(
    step.timestamp ||
    (step.description && step.description !== "No description available") ||
    (step.details && Object.keys(step.details).length > 0)
  );

  return (
    <Card className={`transition-all duration-200 hover:shadow-md ${getStatusColor()}`}>
      <CardContent className="p-4">
        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleTrigger asChild>
            <Button
              variant="ghost"
              className="w-full justify-between p-0 h-auto hover:bg-transparent"
            >
              <div className="flex items-start gap-3 text-left flex-1">
                <div className="flex-shrink-0 mt-0.5">
                  {getStatusIcon()}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-sm">
                      Paso {step.step_number}: {step.action}
                    </span>
                    <Badge variant={getBadgeVariant()} className="text-xs">
                      {step.status === 'completed' ? 'Completado' :
                       step.status === 'error' ? 'Error' : 'En progreso'}
                    </Badge>
                  </div>
                  {/* Solo mostrar descripción básica si hay error, de lo contrario ocultar hasta expandir */}
                  {step.status === 'error' && step.description && step.description !== "No description available" && (
                    <p className="text-sm text-muted-foreground break-words">
                      {step.description}
                    </p>
                  )}
                </div>
              </div>
              {hasDetails && (
                <div className="flex-shrink-0 ml-2">
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </div>
              )}
            </Button>
          </CollapsibleTrigger>

          {hasDetails && (
            <CollapsibleContent className="mt-3 pt-3 border-t border-border/50">
              <div className="space-y-3">
                <h4 className="text-sm font-medium flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  Detalles del paso
                </h4>

                {/* Timestamp */}
                {step.timestamp && (
                  <div className="p-3 bg-slate-50/50 border border-slate-200 rounded-md">
                    <h5 className="text-sm font-medium mb-1">Ejecutado:</h5>
                    <p className="text-sm text-slate-700">
                      {new Date(step.timestamp).toLocaleString('es-ES', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: false
                      })}
                    </p>
                  </div>
                )}

                {/* Descripción completa */}
                {step.description && step.description !== "No description available" && (
                  <div className="p-3 bg-gray-50/50 border border-gray-200 rounded-md">
                    <h5 className="text-sm font-medium mb-1">Descripción:</h5>
                    <p className="text-sm text-gray-700 break-words whitespace-pre-wrap">
                      {step.description}
                    </p>
                  </div>
                )}

                <div className="grid gap-3">
                  {step.details?.error && (
                    <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-md">
                      <h5 className="text-sm font-medium text-destructive mb-1">Error:</h5>
                      <p className="text-sm text-destructive/80 break-words whitespace-pre-wrap">
                        {step.details.error}
                      </p>
                    </div>
                  )}

                  {step.details?.result && (
                    <div className="p-3 bg-muted/50 border rounded-md">
                      <h5 className="text-sm font-medium mb-1">Resultado:</h5>
                      <p className="text-sm text-muted-foreground break-words whitespace-pre-wrap">
                        {step.details.result}
                      </p>
                    </div>
                  )}

                  {step.details?.evaluation && step.details.evaluation !== "Unknown" && (
                    <div className="p-3 bg-blue-50/50 border border-blue-200 rounded-md">
                      <h5 className="text-sm font-medium mb-1">Evaluación:</h5>
                      <p className="text-sm text-blue-700 break-words whitespace-pre-wrap">
                        {step.details.evaluation}
                      </p>
                    </div>
                  )}

                  {step.details?.memory && (
                    <div className="p-3 bg-purple-50/50 border border-purple-200 rounded-md">
                      <h5 className="text-sm font-medium mb-1">Contexto:</h5>
                      <p className="text-sm text-purple-700 break-words whitespace-pre-wrap">
                        {step.details.memory}
                      </p>
                    </div>
                  )}

                  {step.details?.next_goal && (
                    <div className="p-3 bg-green-50/50 border border-green-200 rounded-md">
                      <h5 className="text-sm font-medium mb-1">Objetivo:</h5>
                      <p className="text-sm text-green-700 break-words whitespace-pre-wrap">
                        {step.details.next_goal}
                      </p>
                    </div>
                  )}

                  {step.details?.error_type && (
                    <div className="p-3 bg-orange-50/50 border border-orange-200 rounded-md">
                      <h5 className="text-sm font-medium mb-1">Tipo de error:</h5>
                      <p className="text-sm text-orange-700 break-words">
                        {step.details.error_type}
                      </p>
                      {step.details.error_message && (
                        <p className="text-sm text-orange-600 mt-1 break-words whitespace-pre-wrap">
                          {step.details.error_message}
                        </p>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </CollapsibleContent>
          )}
        </Collapsible>
      </CardContent>
    </Card>
  );
}
