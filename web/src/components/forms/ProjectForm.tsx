"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Project, ProjectCreateInput, ProjectUpdateInput } from "@/lib/types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { XIcon } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/components/ui/use-toast";
import { createGitHubSuite } from "@/lib/services/github-suite";

const projectFormSchema = z.object({
  name: z.string().min(2, {
    message: "Project name must be at least 2 characters.",
  }),
  description: z.string().min(5, {
    message: "Description must be at least 5 characters.",
  }),
  tags: z.array(z.string()).optional(),
  github_config: z
    .object({
      enabled: z.boolean().default(false),
      repo: z.string().min(1, "Repository is required when GitHub is enabled").optional(),
      token: z.string().min(1, "Token is required when GitHub is enabled").optional(),
    })
    .refine(
      (data) => {
        if (data.enabled) {
          return !!data.repo && !!data.token;
        }
        return true;
      },
      {
        message: "Repository and token are required when GitHub integration is enabled",
        path: ["enabled"],
      }
    )
    .optional(),
});

type ProjectFormData = z.infer<typeof projectFormSchema>;

interface ProjectFormProps {
  project?: Project;
  onSubmit: (data: ProjectCreateInput | ProjectUpdateInput) => Promise<void>;
  isSubmitting: boolean;
  submitButtonText?: string;
}

export function ProjectForm({
  project,
  onSubmit,
  isSubmitting,
  submitButtonText = "Create Project",
}: ProjectFormProps) {
  const [currentTag, setCurrentTag] = useState("");
  const { toast } = useToast();

  const form = useForm<ProjectFormData>({
    resolver: zodResolver(projectFormSchema),
    defaultValues: {
      name: project?.name || "",
      description: project?.description || "",
      tags: project?.tags || [],
    },
  });

  const handleAddTag = () => {
    if (currentTag.trim() !== "") {
      const currentTags = form.getValues("tags") || [];
      if (!currentTags.includes(currentTag.trim())) {
        form.setValue("tags", [...currentTags, currentTag.trim()]);
      }
      setCurrentTag("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    const currentTags = form.getValues("tags") || [];
    form.setValue("tags", currentTags.filter((tag) => tag !== tagToRemove));
  };

  const handleSubmit = async (data: ProjectFormData) => {
    try {
      // Si GitHub no está habilitado, eliminamos la configuración
      if (!data.github_config?.enabled) {
        delete data.github_config;
      }
      
      // Si está habilitado pero faltan campos requeridos
      if (data.github_config?.enabled) {
        if (!data.github_config.repo || !data.github_config.token) {
          form.setError("github_config", {
            type: "manual",
            message: "Both repository and token are required when GitHub integration is enabled"
          });
          return;
        }
      }

      // Enviamos el formulario
      await onSubmit(data as ProjectCreateInput);

      // Si GitHub está habilitado y no hay suite_id, creamos la suite
      if (data.github_config?.enabled && !data.github_config.suite_id && project?.project_id) {
        try {
          const suite = await createGitHubSuite(project.project_id);
          // Actualizamos el proyecto con el ID de la suite
          await onSubmit({
            github_config: {
              ...data.github_config,
              suite_id: suite.suite_id
            }
          } as ProjectUpdateInput);
        } catch (error) {
          console.error('Failed to create GitHub suite:', error);
          toast({
            title: "Warning",
            description: "Project saved but failed to create GitHub analysis suite",
            variant: "warning",
          });
        }
      }
    } catch (error) {
      console.error('Form submission error:', error);
      toast({
        title: "Error",
        description: "Failed to save project",
        variant: "destructive",
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{project ? "Edit Project" : "Create New Project"}</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Project Name</FormLabel>
                  <FormControl>
                    <Input placeholder="My Awesome Project" {...field} />
                  </FormControl>
                  <FormDescription>
                    A clear and concise name for your project.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Tell us a little bit about this project"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Brief description of your project.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Tags field */}
            <div className="space-y-4">
              <FormLabel>Tags</FormLabel>
              <div className="flex gap-2">
                <Input
                  placeholder="Add tag"
                  value={currentTag}
                  onChange={(e) => setCurrentTag(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      handleAddTag();
                    }
                  }}
                />
                <Button type="button" onClick={handleAddTag}>
                  Add
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {form.watch("tags")?.map((tag, index) => (
                  <div
                    key={index}
                    className="bg-secondary text-secondary-foreground px-3 py-1 rounded-full flex items-center gap-2"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleRemoveTag(index.toString())}
                      className="hover:text-destructive"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            </div>

            {/* GitHub Configuration Section */}
            <div className="space-y-4 border-t pt-4">
              <h3 className="text-lg font-semibold">GitHub Integration</h3>
              <FormField
                control={form.control}
                name="github_config.enabled"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                    <div className="space-y-0.5">
                      <FormLabel>Enable GitHub Integration</FormLabel>
                      <FormDescription>
                        Connect this project to a GitHub repository
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {form.watch("github_config.enabled") && (
                <>
                  <FormField
                    control={form.control}
                    name="github_config.repo"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>GitHub Repository</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="owner/repository"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Format: username/repository or organization/repository
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="github_config.token"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>GitHub Token</FormLabel>
                        <FormControl>
                          <Input
                            type="password"
                            placeholder="ghp_xxxxxxxxxxxx"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Personal Access Token with repo access
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </>
              )}
            </div>

            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Submitting..." : submitButtonText}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
