import { TestSuite } from '@/lib/types';

export async function createGitHubSuite(projectId: string): Promise<TestSuite> {
  const response = await fetch(`/api/suites`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      project_id: projectId,
      name: 'GitHub PR Analysis',
      description: 'Automated test suite for GitHub Pull Request analysis',
      type: 'github',
      test_cases: []
    }),
  });

  if (!response.ok) {
    throw new Error('Failed to create GitHub suite');
  }

  return response.json();
}
