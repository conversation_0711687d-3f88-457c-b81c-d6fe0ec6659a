// API General
export interface ApiResponse<T> {
  success: boolean;
  count?: number;
  items?: T[];
  error?: string;
  details?: string;
}

export interface ApiErrorResponse {
  success: boolean;
  error: string;
  details?: string;
}

// Project
export interface GitHubConfig {
  enabled: boolean;    // si está habilitada la integración con GitHub
  repo?: string;       // formato: "owner/repo"
  token?: string;      // GitHub Personal Access Token
  suite_id?: string;   // ID de la suite automática de GitHub
}

export interface TestSuite {
  suite_id: string;
  name: string;
  description: string;
  type: 'manual' | 'github' | 'automated';
  created_at: string;
  updated_at: string;
  test_cases?: Record<string, any>;
}

export interface Project {
  project_id: string;
  name: string;
  description: string;
  tags: string[];
  created_at: string;
  updated_at: string;
  test_suites: Record<string, TestSuite>;
  github_config?: GitHubConfig;  // Configuración opcional de GitHub
}

export type ProjectCreateInput = Omit<Project, 'project_id' | 'created_at' | 'updated_at' | 'test_suites'>;
export type ProjectUpdateInput = Partial<Omit<Project, 'project_id' | 'created_at' | 'updated_at' | 'test_suites'>>;

// Test Suite
export interface TestSuite {
  suite_id: string;
  project_id: string;
  name: string;
  description: string;
  tags: string[];
  created_at: string;
  updated_at: string;
  // Test cases can be optional and either a Record or an array
  test_cases?: Record<string, any> | TestCase[];
}

export type TestSuiteCreateInput = Omit<TestSuite, 'suite_id' | 'project_id' | 'created_at' | 'updated_at' | 'test_cases'>;
export type TestSuiteUpdateInput = Partial<Omit<TestSuite, 'suite_id' | 'project_id'| 'created_at' | 'updated_at' | 'test_cases'>>;

// Test Case
export type TestCaseStatus = 'Not Executed' | 'Passed' | 'Failed';

export interface TestCase {
  test_id: string;
  suite_id: string;
  project_id: string;
  name: string;
  description: string;
  instrucciones: string;
  historia_de_usuario: string;
  gherkin?: string;
  url: string;
  tags: string[];
  created_at: string;
  updated_at: string;
  history_files: string[];
  status: TestCaseStatus;
  last_execution: string | null;
  code?: string;
  framework?: string; // "selenium|playwright|cypress"
}

export type TestCaseCreateInput = Omit<TestCase, 'test_id' | 'suite_id' | 'project_id' | 'created_at' | 'updated_at' | 'history_files' | 'status' | 'last_execution' | 'code' | 'framework'>;
export type TestCaseUpdateInput = Partial<TestCaseCreateInput>;
export type TestCaseStatusUpdateInput = { status: TestCaseStatus };

// Test Execution and History
export interface TestExecutionStepResult {
  step: number;
  content: string;
  success?: boolean;  // true if step completed successfully, false if failed, undefined if unknown
  status?: string;    // "completed", "error", or other status strings
}

export interface TestExecutionAction {
  step: number;
  type: string;
  details: Record<string, any> | string;
}

export interface TestExecutionElement {
  step: number;
  tag_name: string;
  xpath: string;
  attributes: Record<string, string>;
}

export interface TestExecutionUrl {
  step: number;
  url: string;
  title: string;
}

export interface TestExecutionMetadata {
  start_time: string | null;
  end_time: string | null;
  total_steps: number;
  success: boolean;
}

export interface TestExecutionHistoryData {
  actions: TestExecutionAction[];
  results: TestExecutionStepResult[];
  elements: TestExecutionElement[];
  urls: TestExecutionUrl[];
  errors: any[];
  screenshots: string[];
  metadata: TestExecutionMetadata;
  test_id?: string;
  execution_id?: string;
  history_path?: string;
  generatedGherkin?: string;
  userStory?: string;
}


export interface TestCaseExecutionResponse {
  success: boolean;
  test_id: string;
  result?: {
    success: boolean;
    test_id: string;
    history_path: string;
    history: TestExecutionHistoryData;
  };
  error: string | null;
}

export interface SuiteExecutionResultItem {
  test_id: string;
  test_name: string;
  result: {
    success: boolean;
    test_id: string;
    history_path: string;
  };
}

export interface SuiteExecutionResponse {
  success: boolean;
  suite_id: string;
  suite_name: string;
  total_tests: number;
  passed: number;
  failed: number;
  results: SuiteExecutionResultItem[];
  execution_time: string;
}

// API Health
export interface ApiHealth {
  status: string;
  version: string;
  timestamp: string;
  api_key_configured: boolean;
}

// AI Tool Types
// Types for AI flows - consolidated here to avoid duplication

// Generate Gherkin types
export type GenerateGherkinInput = {
  instructions: string;
  url?: string;
  userStory?: string;
  language?: string;
};

export type GenerateGherkinOutput = {
  gherkin: string;
};

// Generate Code types
export type GenerateCodeInput = {
  framework: string;
  gherkin_scenario: string;
  test_history?: Record<string, any>;
};

export type GenerateCodeOutput = {
  code: string;
};

// Enhance User Story types
export type EnhanceUserStoryInput = {
  userStory: string;
  language?: string;
};

export type EnhanceUserStoryOutput = {
  enhancedUserStory: string;
};

// Generate Manual Test Cases types
export interface TestCaseObject {
  id: string;
  title: string;
  preconditions: string;
  instrucciones: string | string[];
  expected_results: string;
  priority: string;
  historia_de_usuario: string;
}

export type GenerateManualTestCasesInput = {
  userStory: string;
  language?: string;
};

export type GenerateManualTestCasesOutput = {
  manualTestCases: (string | TestCaseObject)[];
};

// Execute Smoke Test types
export type ExecuteSmokeTestInput = {
  baseUrl: string;
  instructions: string;
  userStory?: string;
  configId?: string;
  configuration?: Record<string, any>;
};

export type ExecuteSmokeTestOutput = TestExecutionHistoryData;

// Summarize Test Results types
export type SummarizeTestResultsInput = {
  testResults: string;
};

export type SummarizeTestResultsOutput = {
  summary: string;
};

// CodeGen types
export type CodegenTargetLanguage = 'javascript' | 'typescript' | 'python' | 'java' | 'csharp';
export type CodegenStatus = 'starting' | 'running' | 'completed' | 'failed' | 'stopped';
export type CodegenColorScheme = 'light' | 'dark';

export interface PlaywrightCodegenRequest {
  // URL y configuración básica
  url?: string;
  target_language: CodegenTargetLanguage;
  
  // Configuración del navegador
  device?: string;
  viewport_size?: string; // "ancho,alto"
  headless?: boolean;
  
  // Configuración de entorno
  timezone?: string;
  geolocation?: string; // "lat,lng"
  language?: string;
  color_scheme?: CodegenColorScheme;
  
  // Gestión de estado
  load_storage?: string;
  save_storage?: string;
  
  // Integración con QAK
  project_id?: string;
  test_suite?: string;
  user_story?: string;
}

export interface CodegenSessionInfo {
  session_id: string;
  status: CodegenStatus;
  target_language: CodegenTargetLanguage;
  url?: string;
  
  // Timestamps
  created_at: string;
  updated_at: string;
  completed_at?: string;
  
  // Resultados
  generated_code?: string;
  error_message?: string;
  
  // Metadatos
  artifacts_path?: string;
  command_used?: string;
  project_integration?: Record<string, any>;
}

export interface CodegenTestCaseRequest {
  session_id: string;
  test_name: string;
  test_description?: string;
  
  // Integración con QAK
  project_id: string;
  test_suite?: string;
  user_story?: string;
  
  // Configuración de conversión
  framework?: string;
  include_assertions?: boolean;
  add_error_handling?: boolean;
}

export interface CodegenStatsResponse {
  total_sessions: number;
  active_sessions: number;
  completed_sessions: number;
  failed_sessions: number;
  
  // Estadísticas por lenguaje
  sessions_by_language: Record<string, number>;
  
  // Promedios
  avg_session_duration?: number;
  avg_generated_lines?: number;
  
  // Última actividad
  last_session_at?: string;
}

export interface CodegenSessionListResponse {
  total_sessions: number;
  sessions: Array<{
    session_id: string;
    status: CodegenStatus;
    target_language: CodegenTargetLanguage;
    url?: string;
    created_at: string;
    updated_at: string;
  }>;
}

export interface CodegenHealthResponse {
  service_status: string;
  playwright_available: boolean;
  playwright_version?: string;
  active_sessions: number;
  total_sessions: number;
  service_uptime: string;
}

// Codegen History Types
export interface CodegenHistorySession {
  session_id: string;
  status: CodegenStatus;
  target_language: CodegenTargetLanguage;
  url?: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
  has_generated_code: boolean;
  command_used?: string;
  error_message?: string;
  project_integration?: {
    project_id: string;
    test_suite?: string;
    user_story?: string;
  };
}

export interface CodegenHistoryResponse {
  total_sessions: number;
  sessions: CodegenHistorySession[];
}

export interface CodegenHistorySessionDetailResponse {
  session_id: string;
  status: CodegenStatus;
  target_language: CodegenTargetLanguage;
  url?: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
  command_used?: string;
  error_message?: string;
  generated_code?: string;
  project_integration?: {
    project_id: string;
    test_suite: string;
    user_story: string;
  };
}

// Codegen Execution Types
export interface CodegenExecutionRequest {
  session_id: string;
  config_id?: string;
  configuration?: Record<string, any>;
}

export interface CodegenExecutionResponse {
  execution_id: string;
  status: string;
  message: string;
}

export interface CodegenExecutionStep {
  step_number: number;
  action: string;
  description: string;
  status: "completed" | "error" | "skipped";
  timestamp?: string;
  details?: {
    error?: string;
    result?: string;
    evaluation?: string;
    memory?: string;
    next_goal?: string;
    error_type?: string;
    error_message?: string;
    step_index?: number;
    [key: string]: any;
  };
}

export interface CodegenExecutionInfo {
  execution_id: string;
  session_id: string;
  status: "starting" | "running" | "completed" | "failed" | "stopped";
  created_at: string;
  updated_at: string;
  completed_at?: string;
  target_url?: string;
  generated_code: string;
  browser_config: Record<string, any>;
  history: any[];
  screenshots?: string[];
  result?: {
    success: boolean;
    total_steps: number;
    analysis: string;
    steps?: CodegenExecutionStep[];
    details?: Record<string, any>;
    error?: string;
  };
  error?: string;
}

export interface CodegenExecutionListResponse {
  total_executions: number;
  executions: Array<{
    execution_id: string;
    session_id: string;
    status: string;
    created_at: string;
    updated_at: string;
    target_url?: string;
  }>;
}

// For forms
export type ProjectFormData = ProjectCreateInput;
export type SuiteFormData = TestSuiteCreateInput;
export type TestCaseFormData = TestCaseCreateInput;

// Utility types
export interface NavItem {
  title: string;
  href: string;
  icon: React.ReactNode;
  disabled?: boolean;
  external?: boolean;
  label?: string;
  items?: NavItem[];
}

