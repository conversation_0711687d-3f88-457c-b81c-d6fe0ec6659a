"use client";

import Link from 'next/link';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getTestCaseById, deleteTestCase, executeTestCaseAsSmokeTest, updateTestCaseStatus } from '@/lib/api';
import type { TestCaseStatus, ExecuteSmokeTestOutput } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/use-toast';
import { TestTube2, ArrowLeft, Edit, Trash2, Play, AlertCircle, CheckCircle, XCircle, Clock, FileText, History, Calendar, Timer, ChevronRight } from 'lucide-react';
import { format } from 'date-fns';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

function TestCaseDetailSkeleton() {
  return (
    <div>
      <Skeleton className="h-9 w-40 mb-4" /> {/* Back button */}
      <div className="flex justify-between items-start mb-4">
        <div>
          <Skeleton className="h-10 w-3/4 mb-1" /> {/* TC Name */}
          <Skeleton className="h-5 w-full mb-3" /> {/* TC Description */}
          <div className="flex flex-wrap gap-2 mb-2">
            <Skeleton className="h-6 w-20 rounded-full" />
          </div>
          <Skeleton className="h-4 w-72 mb-1" /> {/* Dates */}
          <Skeleton className="h-6 w-24 rounded-full mt-2" /> {/* Status Badge */}
        </div>
        <div className="flex flex-col items-end gap-2">
            <div className="flex gap-2">
                <Skeleton className="h-9 w-24" /> {/* Edit button */}
                <Skeleton className="h-9 w-28" /> {/* Delete button */}
            </div>
            <Skeleton className="h-9 w-40" /> {/* Execute Test Case button */}
        </div>
      </div>

      <div className="grid md:grid-cols-2 gap-6 mt-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader><Skeleton className="h-6 w-1/2" /></CardHeader>
            <CardContent><Skeleton className="h-20 w-full" /></CardContent>
          </Card>
        ))}
      </div>
      <Separator className="my-8" />
      <Skeleton className="h-8 w-48 mb-4" /> {/* History Header */}
      <Card><CardContent className="p-6"><Skeleton className="h-10 w-full" /></CardContent></Card>
    </div>
  );
}

// Helper function to extract execution details from filename
function parseExecutionFileName(filename: string) {
  // Extract the filename without path
  const name = filename.split('/').pop() || filename;
  
  // Try to extract timestamp from filename patterns like:
  // smoke_test_20240115143022 or execution_20240115_143022
  const timestampMatch = name.match(/(\d{4})(\d{2})(\d{2})[\-_]?(\d{2})(\d{2})(\d{2})/);
  
  if (timestampMatch) {
    const [, year, month, day, hour, minute, second] = timestampMatch;
    try {
      const date = new Date(
        parseInt(year),
        parseInt(month) - 1, // Month is 0-indexed
        parseInt(day),
        parseInt(hour),
        parseInt(minute),
        parseInt(second)
      );
      return {
        date,
        displayName: `Execution ${format(date, 'MMM d, yyyy HH:mm:ss')}`,
        isValid: !isNaN(date.getTime())
      };
    } catch (e) {
      // If date parsing fails, fall back to original name
    }
  }
  
  // Fallback: try to make the filename more readable
  const cleanName = name
    .replace(/\.json$/, '')
    .replace(/[_\-]/g, ' ')
    .replace(/smoke test/i, 'Smoke Test')
    .replace(/execution/i, 'Execution');
  
  return {
    date: null,
    displayName: cleanName,
    isValid: false
  };
}

function ExecutionHistoryCard({ file, index, projectId, suiteId, testId }: {
  file: string;
  index: number;
  projectId: string;
  suiteId: string;
  testId: string;
}) {
  const execution = parseExecutionFileName(file);
  
  return (
    <Card className="hover:shadow-md transition-all duration-200 hover:border-primary/20">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-primary/10">
              <Timer className="h-4 w-4 text-primary" />
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-sm">
                {execution.displayName}
              </h4>
              <div className="flex items-center gap-2 mt-1">
                {execution.isValid && execution.date ? (
                  <>
                    <Calendar className="h-3 w-3 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">
                      {format(execution.date, 'MMM d, yyyy')}
                    </span>
                    <Clock className="h-3 w-3 text-muted-foreground ml-2" />
                    <span className="text-xs text-muted-foreground">
                      {format(execution.date, 'HH:mm:ss')}
                    </span>
                  </>
                ) : (
                  <>
                    <FileText className="h-3 w-3 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">
                      Record #{index + 1}
                    </span>
                  </>
                )}
              </div>
            </div>
          </div>
          <Button 
            variant="ghost" 
            size="sm" 
            asChild
            className="hover:bg-primary/10"
          >
            <Link 
              href={`/projects/${projectId}/suites/${suiteId}/tests/${testId}/execute?historyPath=${encodeURIComponent(file)}`}
              className="flex items-center gap-2"
            >
              <span className="text-sm">View Details</span>
              <ChevronRight className="h-4 w-4" />
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

export default function TestCaseDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const projectId = params.projectId as string;
  const suiteId = params.suiteId as string;
  const testId = params.testId as string;

  const { data: testCase, isLoading, error, isError, refetch } = useQuery({
    queryKey: ['testCase', projectId, suiteId, testId],
    queryFn: () => getTestCaseById(projectId, suiteId, testId),
    enabled: !!projectId && !!suiteId && !!testId,
  });

  const deleteMutation = useMutation({
    mutationFn: () => deleteTestCase(projectId, suiteId, testId),
    onSuccess: () => {
      toast({ title: "Test Case Deleted", description: `Test Case "${testCase?.name}" has been deleted.` });
      queryClient.invalidateQueries({ queryKey: ['testCases', projectId, suiteId] });
      router.push(`/projects/${projectId}/suites/${suiteId}`);
    },
    onError: (err) => {
      toast({ title: "Error Deleting Test Case", description: err.message, variant: "destructive" });
    },
  });

  const executeMutation = useMutation({
    mutationFn: () => executeTestCaseAsSmokeTest(projectId, suiteId, testId),
    onSuccess: (data: ExecuteSmokeTestOutput) => {
      toast({
        title: "Smoke Test Execution Initiated",
        description: `Execution for "${testCase?.name}" started.`,
      });
      // Store the execution data for the execution page to use
      // This ensures we use the same data structure as the smoke test playground
      queryClient.setQueryData(['executionResult', testId, 'latest'], data);
      router.push(`/projects/${projectId}/suites/${suiteId}/tests/${testId}/execute?executionId=latest`);
      refetch(); // Refetch test case to update status and last execution
    },
    onError: (err) => {
      toast({ title: "Error Executing Smoke Test", description: err.message, variant: "destructive" });
    },
  });

  const updateStatusMutation = useMutation({
    mutationFn: (newStatus: TestCaseStatus) => updateTestCaseStatus(projectId, suiteId, testId, { status: newStatus }),
    onSuccess: (updatedTc) => {
      queryClient.setQueryData(['testCase', projectId, suiteId, testId], updatedTc);
      queryClient.invalidateQueries({queryKey: ['testCases', projectId, suiteId]});
      toast({ title: "Status Updated", description: `Test Case status changed to ${updatedTc.status}.` });
    },
    onError: (err) => {
      toast({ title: "Error Updating Status", description: err.message, variant: "destructive" });
    }
  });


  if (isLoading) return <TestCaseDetailSkeleton />;
  if (isError) return <Alert variant="destructive"><AlertCircle className="h-4 w-4" /><AlertTitle>Error</AlertTitle><AlertDescription>{error.message}</AlertDescription></Alert>;
  if (!testCase) return <p>Test Case not found.</p>;

  let StatusIcon = Clock;
  if (testCase.status === 'Passed') StatusIcon = CheckCircle;
  else if (testCase.status === 'Failed') StatusIcon = XCircle;

  const detailSections = [
    { title: "Instructions", content: testCase.instrucciones, icon: <FileText className="h-5 w-5 text-primary" /> },
    { title: "User Story", content: testCase.historia_de_usuario, icon: <FileText className="h-5 w-5 text-primary" /> },
    { title: "Target URL", content: testCase.url, icon: <FileText className="h-5 w-5 text-primary" /> },
    { title: "Gherkin Scenario", content: testCase.gherkin || "Not specified", icon: <FileText className="h-5 w-5 text-primary" /> },
  ];

  // Sort execution files by date (newest first) if possible
  const sortedHistoryFiles = testCase.history_files 
    ? [...testCase.history_files].sort((a, b) => {
        const dateA = parseExecutionFileName(a);
        const dateB = parseExecutionFileName(b);
        
        if (dateA.isValid && dateB.isValid && dateA.date && dateB.date) {
          return dateB.date.getTime() - dateA.date.getTime(); // Newest first
        }
        
        // Fallback to original order for non-parseable filenames
        return 0;
      })
    : [];

  return (
    <div>
      <Button variant="outline" size="sm" asChild className="mb-4">
        <Link href={`/projects/${projectId}/suites/${suiteId}`}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Suite
        </Link>
      </Button>

      <div className="flex flex-col md:flex-row justify-between md:items-start mb-4">
        <div className="flex-1">
          <h1 className="page-header mb-1 flex items-center gap-2">
            <TestTube2 className="h-8 w-8 text-primary" />
            {testCase.name}
          </h1>
          <p className="text-muted-foreground text-sm mb-3">{testCase.description}</p>
          <div className="flex flex-wrap gap-2 mb-2">
            {testCase.tags.map((tag) => (
              <Badge key={tag} variant="outline">{tag}</Badge>
            ))}
          </div>
          <p className="text-xs text-muted-foreground">
            Created: {format(new Date(testCase.created_at), 'PPP p')} | Updated: {format(new Date(testCase.updated_at), 'PPP p')}
          </p>
           {testCase.last_execution && (
            <p className="text-xs text-muted-foreground mt-1">
              Last Execution: {format(new Date(testCase.last_execution), 'PPP p')}
            </p>
          )}
          <div className="mt-3 flex items-center gap-2">
            <span className="text-sm font-medium">Status:</span>
             <Select
                value={testCase.status}
                onValueChange={(value: TestCaseStatus) => updateStatusMutation.mutate(value)}
                disabled={updateStatusMutation.isPending}
              >
                <SelectTrigger className="w-[180px] h-8">
                  <div className="flex items-center">

                    <SelectValue placeholder="Set status" />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Not Executed"><Clock className="mr-2 h-4 w-4 text-gray-500 inline-block"/>Not Executed</SelectItem>
                  <SelectItem value="Passed"><CheckCircle className="mr-2 h-4 w-4 text-green-500 inline-block"/>Passed</SelectItem>
                  <SelectItem value="Failed"><XCircle className="mr-2 h-4 w-4 text-red-500 inline-block"/>Failed</SelectItem>
                </SelectContent>
              </Select>
          </div>
        </div>
         <div className="flex flex-col items-start md:items-end gap-2 mt-4 md:mt-0">
          <div className="flex gap-2">
            <Button variant="outline" size="sm" disabled> {/* TODO: Link to edit page */}
              <Edit className="mr-2 h-4 w-4" /> Edit
            </Button>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" size="sm">
                  <Trash2 className="mr-2 h-4 w-4" /> Delete
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete the test case "{testCase.name}".
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={() => deleteMutation.mutate()} disabled={deleteMutation.isPending}>
                    {deleteMutation.isPending ? "Deleting..." : "Delete Test Case"}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
          <Button
            size="sm"
            onClick={() => executeMutation.mutate()}
            disabled={executeMutation.isPending}
            className="w-full md:w-auto bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600"
          >
            <Play className="mr-2 h-4 w-4" />
            {executeMutation.isPending ? "Executing..." : "Execute Smoke Test"}
          </Button>
        </div>
      </div>
      
      <div className="grid md:grid-cols-2 gap-6 mt-6">
        {detailSections.map(section => (
          <Card key={section.title}>
            <CardHeader className="flex flex-row items-center gap-2 pb-2">
              {section.icon}
              <CardTitle className="text-lg">{section.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="text-sm whitespace-pre-wrap font-sans bg-muted p-3 rounded-md max-h-60 overflow-y-auto">{section.content}</pre>
            </CardContent>
          </Card>
        ))}
      </div>

      <Separator className="my-8" />

      <div>
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 rounded-lg bg-primary/10">
            <History className="h-6 w-6 text-primary"/>
          </div>
          <div>
            <h2 className="section-header mb-0">Execution History</h2>
            <p className="text-sm text-muted-foreground">
              {sortedHistoryFiles.length > 0 
                ? `${sortedHistoryFiles.length} execution record${sortedHistoryFiles.length > 1 ? 's' : ''} found`
                : 'No execution records available'
              }
            </p>
          </div>
        </div>
        
        {sortedHistoryFiles.length > 0 ? (
          <div className="space-y-3">
            {sortedHistoryFiles.map((file, index) => (
              <ExecutionHistoryCard
                key={file}
                file={file}
                index={index}
                projectId={projectId}
                suiteId={suiteId}
                testId={testId}
              />
            ))}
          </div>
        ) : (
          <Card className="border-dashed">
            <CardContent className="p-8 text-center">
              <div className="flex flex-col items-center gap-4">
                <div className="p-4 rounded-full bg-muted/50">
                  <History className="h-8 w-8 text-muted-foreground" />
                </div>
                <div>
                  <h3 className="font-medium text-lg mb-1">No Execution History</h3>
                  <p className="text-muted-foreground">
                    This test case hasn't been executed yet. Click "Execute Smoke Test" to run your first test.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
