"use client";

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';

import type { PlaywrightCodegenRequest } from '@/lib/types';

const formSchema = z.object({
  url: z.string().url().optional().or(z.literal('')),
  target_language: z.enum(['javascript', 'typescript', 'python', 'java', 'csharp']),
  device: z.string().optional(),
  viewport_size: z.string().regex(/^\d+,\d+$/).optional().or(z.literal('')),
  headless: z.boolean(),
  timezone: z.string().optional(),
  geolocation: z.string().regex(/^-?\d+\.?\d*,-?\d+\.?\d*$/).optional().or(z.literal('')),
  language: z.string().optional(),
  color_scheme: z.enum(['light', 'dark']).optional(),
  load_storage: z.string().optional(),
  save_storage: z.string().optional(),
  project_id: z.string().optional(),
  test_suite: z.string().optional(),
  user_story: z.string().optional(),
});

type FormData = z.infer<typeof formSchema>;

interface NewSessionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onStartSession: (request: PlaywrightCodegenRequest) => void;
  isStarting: boolean;
}

export function NewSessionDialog({
  open,
  onOpenChange,
  onStartSession,
  isStarting
}: NewSessionDialogProps) {
  const [activeTab, setActiveTab] = useState("basic");

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      target_language: 'javascript',
      headless: true, // Default to headless mode for Playwright Codegen
      url: '',
      viewport_size: '',
      geolocation: '',
      load_storage: '',
      save_storage: '',
      project_id: '',
      test_suite: '',
      user_story: '',
    },
  });

  const onSubmit = (data: FormData) => {
    const request: PlaywrightCodegenRequest = {
      ...data,
      url: data.url || undefined,
      viewport_size: data.viewport_size || undefined,
      geolocation: data.geolocation || undefined,
      load_storage: data.load_storage || undefined,
      save_storage: data.save_storage || undefined,
      project_id: data.project_id || undefined,
      test_suite: data.test_suite || undefined,
      user_story: data.user_story || undefined,
    };
    onStartSession(request);
  };

  const commonDevices = [
    { label: 'Desktop Chrome', value: 'Desktop Chrome' },
    { label: 'Desktop Firefox', value: 'Desktop Firefox' },
    { label: 'Desktop Safari', value: 'Desktop Safari' },
    { label: 'iPhone 13', value: 'iPhone 13' },
    { label: 'iPhone 13 Pro', value: 'iPhone 13 Pro' },
    { label: 'iPad Pro', value: 'iPad Pro' },
    { label: 'Galaxy S21', value: 'Galaxy S21' },
    { label: 'Pixel 5', value: 'Pixel 5' },
  ];

  const timezones = [
    { label: 'America/New_York', value: 'America/New_York' },
    { label: 'America/Los_Angeles', value: 'America/Los_Angeles' },
    { label: 'Europe/London', value: 'Europe/London' },
    { label: 'Europe/Paris', value: 'Europe/Paris' },
    { label: 'Asia/Tokyo', value: 'Asia/Tokyo' },
    { label: 'Asia/Shanghai', value: 'Asia/Shanghai' },
    { label: 'Australia/Sydney', value: 'Australia/Sydney' },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Start Recording Session</DialogTitle>
          <DialogDescription>
            Playwright will run in headless mode and automatically record interactions based on the provided URL. The generated test code will be available after the session completes.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="basic">Basic</TabsTrigger>
                <TabsTrigger value="browser">Browser</TabsTrigger>
                <TabsTrigger value="integration">Integration</TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-4">
                <FormField
                  control={form.control}
                  name="target_language"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Target Language</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select target language" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="javascript">JavaScript</SelectItem>
                          <SelectItem value="typescript">TypeScript</SelectItem>
                          <SelectItem value="python">Python</SelectItem>
                          <SelectItem value="java">Java</SelectItem>
                          <SelectItem value="csharp">C#</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        The programming language for generated test code
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Starting URL (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="https://example.com" {...field} />
                      </FormControl>
                      <FormDescription>
                        The initial URL to navigate to when starting the recording
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

              </TabsContent>

              <TabsContent value="browser" className="space-y-4">
                <FormField
                  control={form.control}
                  name="device"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Device Emulation</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select device to emulate" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {commonDevices.map((device) => (
                            <SelectItem key={device.value} value={device.value}>
                              {device.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Emulate a specific device during recording
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="viewport_size"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Viewport Size</FormLabel>
                      <FormControl>
                        <Input placeholder="1920,1080" {...field} />
                      </FormControl>
                      <FormDescription>
                        Browser viewport size as width,height (e.g., "1920,1080")
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="timezone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Timezone</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select timezone" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {timezones.map((timezone) => (
                            <SelectItem key={timezone.value} value={timezone.value}>
                              {timezone.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Browser timezone for the recording session
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="geolocation"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Geolocation</FormLabel>
                      <FormControl>
                        <Input placeholder="40.7128,-74.0060" {...field} />
                      </FormControl>
                      <FormDescription>
                        Browser geolocation as latitude,longitude (e.g., "40.7128,-74.0060")
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="language"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Browser Language</FormLabel>
                      <FormControl>
                        <Input placeholder="en-US" {...field} />
                      </FormControl>
                      <FormDescription>
                        Browser language setting (e.g., "en-US", "es-ES")
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="color_scheme"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Color Scheme</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select color scheme" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="light">Light</SelectItem>
                          <SelectItem value="dark">Dark</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Browser color scheme preference
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>

              <TabsContent value="integration" className="space-y-4">
                <FormField
                  control={form.control}
                  name="project_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Project ID</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter project ID" {...field} />
                      </FormControl>
                      <FormDescription>
                        QAK project to associate with this recording
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="test_suite"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Test Suite</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter test suite name" {...field} />
                      </FormControl>
                      <FormDescription>
                        Test suite name for organizing the generated test case
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="user_story"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>User Story</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="As a user, I want to..."
                          className="min-h-[100px]"
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        User story or description for this test case
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Separator />

                <div className="space-y-4">
                  <h4 className="text-sm font-medium">State Management</h4>
                  
                  <FormField
                    control={form.control}
                    name="load_storage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Load Storage State</FormLabel>
                        <FormControl>
                          <Input placeholder="/path/to/storage-state.json" {...field} />
                        </FormControl>
                        <FormDescription>
                          Load browser storage state from file (cookies, localStorage, etc.)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="save_storage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Save Storage State</FormLabel>
                        <FormControl>
                          <Input placeholder="/path/to/save-storage.json" {...field} />
                        </FormControl>
                        <FormDescription>
                          Save browser storage state to file after recording
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </TabsContent>
            </Tabs>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isStarting}>
                {isStarting ? "Starting Session..." : "Start Recording"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
