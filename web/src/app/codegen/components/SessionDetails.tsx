"use client";

import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Download, 
  RefreshCw, 
  Code, 
  FileText, 
  Clock, 
  Calendar,
  Monitor,
  Copy,
  Check,
  ExternalLink,
  Play,
  ArrowRight,
  BarChart3,
  CheckCircle2,
  XCircle,
  Info,
  Camera,
  Eye
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

import {
  getCodegenSession,
  getCodegenGeneratedCode,
  convertCodegenToTestcase,
  executeCodegenTest,
  getCodegenExecution,
  stopCodegenExecution
} from '@/lib/api';

import type { 
  CodegenSessionInfo, 
  CodegenTestCaseRequest,
  CodegenExecutionInfo 
} from '@/lib/types';

interface SessionDetailsProps {
  sessionId: string;
  onRefresh: () => void;
}

export function SessionDetails({ sessionId, onRefresh }: SessionDetailsProps) {
  const [copiedCode, setCopiedCode] = useState(false);
  const [showConvertDialog, setShowConvertDialog] = useState(false);
  const [showExecuteDialog, setShowExecuteDialog] = useState(false);
  const [activeExecution, setActiveExecution] = useState<string | null>(null);
  const [convertFormData, setConvertFormData] = useState({
    test_name: '',
    test_description: '',
    project_id: '',
    test_suite: '',
    user_story: '',
    framework: 'playwright'
  });
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: session, isLoading } = useQuery({
    queryKey: ['codegen-session', sessionId],
    queryFn: () => getCodegenSession(sessionId),
    refetchInterval: 5000,
    enabled: !!sessionId,
  });

  const { data: codeData, isLoading: codeLoading } = useQuery({
    queryKey: ['codegen-code', sessionId],
    queryFn: () => getCodegenGeneratedCode(sessionId),
    enabled: !!sessionId && (session?.status === 'completed' || session?.status === 'stopped'),
  });

  // Execution query - only when there's an active execution
  const { data: executionData } = useQuery({
    queryKey: ['codegen-execution', activeExecution],
    queryFn: () => getCodegenExecution(activeExecution!),
    enabled: !!activeExecution,
    refetchInterval: 2000, // Refetch more frequently for execution status
  });

  const convertMutation = useMutation({
    mutationFn: convertCodegenToTestcase,
    onSuccess: (data) => {
      toast({
        title: "Test Case Created",
        description: `Successfully converted session to test case: ${data.test_name}`,
      });
      setShowConvertDialog(false);
      queryClient.invalidateQueries({ queryKey: ['projects'] });
    },
    onError: (error) => {
      toast({
        title: "Conversion Failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const executeMutation = useMutation({
    mutationFn: executeCodegenTest,
    onSuccess: (data) => {
      toast({
        title: "Test Execution Started",
        description: "Your test is now running with browser-use",
      });
      setActiveExecution(data.execution_id);
      setShowExecuteDialog(false);
    },
    onError: (error) => {
      toast({
        title: "Execution Failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const stopExecutionMutation = useMutation({
    mutationFn: stopCodegenExecution,
    onSuccess: () => {
      toast({
        title: "Execution Stopped",
        description: "Test execution has been stopped",
      });
      queryClient.invalidateQueries({ queryKey: ['codegen-execution', activeExecution] });
    },
    onError: (error) => {
      toast({
        title: "Stop Failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleCopyCode = async () => {
    if (codeData?.generated_code) {
      await navigator.clipboard.writeText(codeData.generated_code);
      setCopiedCode(true);
      setTimeout(() => setCopiedCode(false), 2000);
      toast({
        title: "Code Copied",
        description: "Generated code copied to clipboard",
      });
    }
  };

  const handleConvert = () => {
    const request: CodegenTestCaseRequest = {
      session_id: sessionId,
      test_name: convertFormData.test_name,
      test_description: convertFormData.test_description,
      project_id: convertFormData.project_id,
      test_suite: convertFormData.test_suite || undefined,
      user_story: convertFormData.user_story || undefined,
      framework: convertFormData.framework,
      include_assertions: true,
      add_error_handling: true,
    };
    convertMutation.mutate(request);
  };

  const handleExecute = () => {
    executeMutation.mutate({ session_id: sessionId });
  };

  const handleStopExecution = () => {
    if (activeExecution) {
      stopExecutionMutation.mutate(activeExecution);
    }
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'starting':
        return 'secondary';
      case 'running':
        return 'default';
      case 'completed':
        return 'success';
      case 'failed':
        return 'destructive';
      case 'stopped':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-32" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-40 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!session) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64 text-muted-foreground">
          Session not found
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Monitor className="h-5 w-5" />
                Session Details
              </CardTitle>
              <CardDescription>
                Session {session.session_id.slice(0, 8)}...
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={getStatusColor(session.status) as any}>
                {session.status}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={onRefresh}
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="info" className="space-y-4">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="info">
                <FileText className="h-4 w-4 mr-2" />
                Info
              </TabsTrigger>
              <TabsTrigger value="code" disabled={!codeData?.generated_code}>
                <Code className="h-4 w-4 mr-2" />
                Code
              </TabsTrigger>
              <TabsTrigger value="execute" disabled={!codeData?.generated_code}>
                <Play className="h-4 w-4 mr-2" />
                Execute
              </TabsTrigger>
              <TabsTrigger value="results" disabled={!executionData || executionData.status === 'starting'}>
                <BarChart3 className="h-4 w-4 mr-2" />
                Results
              </TabsTrigger>
              <TabsTrigger value="convert" disabled={session.status !== 'completed' && session.status !== 'stopped'}>
                <ArrowRight className="h-4 w-4 mr-2" />
                Convert
              </TabsTrigger>
            </TabsList>

            <TabsContent value="info" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">Status</Label>
                  <Badge variant={getStatusColor(session.status) as any} className="w-fit">
                    {session.status}
                  </Badge>
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">Language</Label>
                  <Badge variant="outline" className="w-fit">
                    {session.target_language}
                  </Badge>
                </div>
              </div>

              {session.url && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">Target URL</Label>
                  <div className="flex items-center gap-2">
                    <Input value={session.url} readOnly className="font-mono text-sm" />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(session.url, '_blank')}
                    >
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Created
                  </Label>
                  <Input 
                    value={formatDateTime(session.created_at)} 
                    readOnly 
                    className="text-sm"
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Updated
                  </Label>
                  <Input 
                    value={formatDateTime(session.updated_at)} 
                    readOnly 
                    className="text-sm"
                  />
                </div>
              </div>

              {session.completed_at && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">Completed</Label>
                  <Input 
                    value={formatDateTime(session.completed_at)} 
                    readOnly 
                    className="text-sm"
                  />
                </div>
              )}

              {session.error_message && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-destructive">Error</Label>
                  <Textarea 
                    value={session.error_message} 
                    readOnly 
                    className="text-sm font-mono text-destructive"
                    rows={3}
                  />
                </div>
              )}

              {session.command_used && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">Command</Label>
                  <Input 
                    value={session.command_used} 
                    readOnly 
                    className="text-sm font-mono"
                  />
                </div>
              )}
            </TabsContent>

            <TabsContent value="code" className="space-y-4">
              {codeLoading ? (
                <Skeleton className="h-64 w-full" />
              ) : codeData?.generated_code ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium">Generated Code</Label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCopyCode}
                    >
                      {copiedCode ? (
                        <Check className="h-4 w-4 mr-2" />
                      ) : (
                        <Copy className="h-4 w-4 mr-2" />
                      )}
                      {copiedCode ? 'Copied' : 'Copy'}
                    </Button>
                  </div>
                  <ScrollArea className="h-96 w-full rounded border">
                    <pre className="p-4 text-sm font-mono">
                      <code>{codeData.generated_code}</code>
                    </pre>
                  </ScrollArea>
                </div>
              ) : (
                <div className="flex items-center justify-center h-32 text-muted-foreground">
                  <div className="text-center">
                    <Code className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No code generated yet</p>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="execute" className="space-y-4">
              {!activeExecution ? (
                <div className="text-center py-8">
                  <Play className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-medium mb-2">Execute with Browser-Use</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Run your recorded test using browser-use AI agent for automated execution
                  </p>
                  <Button 
                    onClick={() => setShowExecuteDialog(true)}
                    disabled={!codeData?.generated_code || executeMutation.isPending}
                  >
                    <Play className="h-4 w-4 mr-2" />
                    {executeMutation.isPending ? 'Starting...' : 'Execute Test'}
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium">Test Execution</h3>
                    <div className="flex items-center gap-2">
                      <Badge variant={
                        executionData?.status === 'running' ? 'default' :
                        executionData?.status === 'completed' ? 'default' :
                        executionData?.status === 'failed' ? 'destructive' : 'secondary'
                      }>
                        {executionData?.status || 'loading'}
                      </Badge>
                      {executionData?.status === 'running' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleStopExecution}
                          disabled={stopExecutionMutation.isPending}
                        >
                          <RefreshCw className="h-4 w-4 mr-2" />
                          Stop
                        </Button>
                      )}
                    </div>
                  </div>

                  {executionData && (
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label className="text-sm font-medium text-muted-foreground">Execution ID</Label>
                        <Input 
                          value={executionData.execution_id} 
                          readOnly 
                          className="text-sm font-mono"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium text-muted-foreground">Status</Label>
                        <Input 
                          value={executionData.status} 
                          readOnly 
                          className="text-sm"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium text-muted-foreground">Created</Label>
                        <Input 
                          value={formatDateTime(executionData.created_at)} 
                          readOnly 
                          className="text-sm"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium text-muted-foreground">Updated</Label>
                        <Input 
                          value={formatDateTime(executionData.updated_at)} 
                          readOnly 
                          className="text-sm"
                        />
                      </div>
                    </div>
                  )}

                  {executionData?.status === 'running' && (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-muted-foreground">Target URL</Label>
                      <Input 
                        value={executionData.target_url || ''} 
                        readOnly 
                        className="text-sm font-mono"
                      />
                    </div>
                  )}

                  {executionData?.status === 'completed' && (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-muted-foreground">Result</Label>
                      <Badge variant={executionData.result?.success ? 'default' : 'destructive'} className="w-fit">
                        {executionData.result?.success ? 'Passed' : 'Failed'}
                      </Badge>
                      {executionData.result?.analysis && (
                        <p className="text-sm text-muted-foreground">{executionData.result.analysis}</p>
                      )}
                    </div>
                  )}

                  {executionData?.status === 'failed' && executionData.error && (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-muted-foreground">Error</Label>
                      <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-md">
                        <p className="text-sm text-destructive">{executionData.error}</p>
                      </div>
                    </div>
                  )}

                  <div className="flex justify-end gap-2">
                    <Button 
                      variant="outline" 
                      onClick={() => setActiveExecution(null)}
                    >
                      New Execution
                    </Button>
                    {executionData?.history && executionData.history.length > 0 && (
                      <Button variant="outline">
                        <Download className="h-4 w-4 mr-2" />
                        Download History
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="results" className="space-y-4">
              {!executionData ? (
                <div className="text-center py-8">
                  <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-medium mb-2">No Results Available</h3>
                  <p className="text-sm text-muted-foreground">
                    Execute your test first to see detailed results
                  </p>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Overall Summary */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BarChart3 className="h-5 w-5" />
                        Execution Summary
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Card className="bg-muted/50">
                          <CardHeader className="pb-2">
                            <CardDescription>Status</CardDescription>
                            <CardTitle className="text-xl flex items-center gap-2">
                              {executionData.status === 'completed' ? (
                                <CheckCircle2 className="h-6 w-6 text-green-500" />
                              ) : executionData.status === 'failed' ? (
                                <XCircle className="h-6 w-6 text-red-500" />
                              ) : (
                                <Info className="h-6 w-6 text-blue-500" />
                              )}
                              {executionData.status}
                            </CardTitle>
                          </CardHeader>
                        </Card>
                        
                        <Card className="bg-muted/50">
                          <CardHeader className="pb-2">
                            <CardDescription>Success Rate</CardDescription>
                            <CardTitle className="text-xl">
                              {executionData.result?.success ? '100%' : '0%'}
                            </CardTitle>
                          </CardHeader>
                        </Card>

                        <Card className="bg-muted/50">
                          <CardHeader className="pb-2">
                            <CardDescription>Total Steps</CardDescription>
                            <CardTitle className="text-xl">
                              {executionData.result?.total_steps || 0}
                            </CardTitle>
                          </CardHeader>
                        </Card>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Result Analysis */}
                  {executionData.result && (
                    <Card>
                      <CardHeader>
                        <CardTitle>Analysis</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="p-4 rounded-md border-l-4 border-l-primary bg-muted/20">
                            <p className="text-sm font-medium mb-2">Result</p>
                            <p className="text-sm">{executionData.result.analysis}</p>
                          </div>
                          
                          {executionData.result.details && (
                            <div className="grid grid-cols-1 gap-4">
                              <div className="space-y-2">
                                <Label className="text-sm font-medium">Execution Summary</Label>
                                <p className="text-sm text-muted-foreground">
                                  {executionData.result.details.execution_summary}
                                </p>
                              </div>
                              
                              {executionData.result.details.history_type && (
                                <div className="space-y-2">
                                  <Label className="text-sm font-medium">History Type</Label>
                                  <Badge variant="outline">
                                    {executionData.result.details.history_type}
                                  </Badge>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Detailed Steps */}
                  {executionData.result?.steps && executionData.result.steps.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Play className="h-5 w-5" />
                          Execution Steps
                        </CardTitle>
                        <CardDescription>
                          Detailed breakdown of each step executed
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {executionData.result.steps.map((step, index) => (
                            <div
                              key={index}
                              className="flex items-start gap-4 p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                            >
                              <div className="flex-shrink-0">
                                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                                  step.status === 'completed' ? 'bg-green-100 text-green-700' :
                                  step.status === 'error' ? 'bg-red-100 text-red-700' :
                                  'bg-gray-100 text-gray-700'
                                }`}>
                                  {step.status === 'completed' ? (
                                    <CheckCircle2 className="h-4 w-4" />
                                  ) : step.status === 'error' ? (
                                    <XCircle className="h-4 w-4" />
                                  ) : (
                                    step.step_number
                                  )}
                                </div>
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-1">
                                  <p className="text-sm font-medium">
                                    Step {step.step_number}: {step.action}
                                  </p>
                                  <Badge variant={step.status === 'completed' ? 'default' : step.status === 'error' ? 'destructive' : 'secondary'}>
                                    {step.status}
                                  </Badge>
                                </div>
                                <p className="text-sm text-muted-foreground">
                                  {step.description}
                                </p>
                                {step.timestamp && (
                                  <p className="text-xs text-muted-foreground mt-1">
                                    {new Date(step.timestamp).toLocaleString()}
                                  </p>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Screenshots Gallery */}
                  {executionData.screenshots && executionData.screenshots.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Camera className="h-5 w-5" />
                          Screenshots
                        </CardTitle>
                        <CardDescription>
                          Visual evidence of each step execution
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                          {executionData.screenshots.map((screenshot, index) => (
                            <div key={index} className="space-y-2">
                              <div className="relative group">
                                <img
                                  src={screenshot}
                                  alt={`Step ${index + 1} screenshot`}
                                  className="w-full h-48 object-cover rounded-lg border cursor-pointer hover:opacity-80 transition-opacity"
                                  onClick={() => window.open(screenshot, '_blank')}
                                />
                                <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                                  <Eye className="h-6 w-6 text-white" />
                                </div>
                              </div>
                              <div className="text-center">
                                <p className="text-sm font-medium">Step {index + 1}</p>
                                <p className="text-xs text-muted-foreground">
                                  Click to view full size
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Error Details */}
                  {executionData.error && (
                    <Card className="border-destructive">
                      <CardHeader>
                        <CardTitle className="text-destructive flex items-center gap-2">
                          <XCircle className="h-5 w-5" />
                          Error Details
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-md">
                          <p className="text-sm text-destructive font-mono">{executionData.error}</p>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Generated Code Display */}
                  {executionData.generated_code && (
                    <Card>
                      <CardHeader>
                        <CardTitle>Executed Code</CardTitle>
                        <CardDescription>
                          The Playwright code that was converted and executed
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <ScrollArea className="h-64 w-full rounded border">
                          <pre className="p-4 text-sm font-mono">
                            <code>{executionData.generated_code}</code>
                          </pre>
                        </ScrollArea>
                      </CardContent>
                    </Card>
                  )}

                  {/* Browser Configuration */}
                  {executionData.browser_config && Object.keys(executionData.browser_config).length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle>Browser Configuration</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ScrollArea className="h-32 w-full rounded border">
                          <pre className="p-4 text-sm font-mono">
                            <code>{JSON.stringify(executionData.browser_config, null, 2)}</code>
                          </pre>
                        </ScrollArea>
                      </CardContent>
                    </Card>
                  )}

                  {/* Execution Timeline */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Timeline</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between p-3 border rounded-md">
                          <div className="flex items-center gap-3">
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <div>
                              <p className="text-sm font-medium">Execution Started</p>
                              <p className="text-xs text-muted-foreground">
                                {formatDateTime(executionData.created_at)}
                              </p>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between p-3 border rounded-md">
                          <div className="flex items-center gap-3">
                            <div className={`w-2 h-2 rounded-full ${
                              executionData.status === 'completed' ? 'bg-green-500' : 
                              executionData.status === 'failed' ? 'bg-red-500' : 
                              'bg-yellow-500'
                            }`}></div>
                            <div>
                              <p className="text-sm font-medium">
                                Execution {executionData.status === 'completed' ? 'Completed' : 
                                         executionData.status === 'failed' ? 'Failed' : 'In Progress'}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {formatDateTime(executionData.updated_at)}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Action Buttons */}
                  <div className="flex justify-end gap-2">
                    <Button 
                      variant="outline" 
                      onClick={() => setActiveExecution(null)}
                    >
                      New Execution
                    </Button>
                    <Button 
                      variant="outline"
                      onClick={() => {
                        const data = JSON.stringify(executionData, null, 2);
                        const blob = new Blob([data], { type: 'application/json' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `codegen-execution-${executionData.execution_id}.json`;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);
                      }}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download Results
                    </Button>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="convert" className="space-y-4">
              <div className="text-center py-8">
                <ArrowRight className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-medium mb-2">Convert to QAK Test Case</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Transform your recorded interactions into a reusable QAK test case that can be executed with browser-use
                </p>
                <Button 
                  onClick={() => setShowConvertDialog(true)}
                  disabled={session.status !== 'completed' && session.status !== 'stopped'}
                >
                  <Play className="h-4 w-4 mr-2" />
                  Convert to Test Case
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Execute Dialog */}
      <Dialog open={showExecuteDialog} onOpenChange={setShowExecuteDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Execute Test with Browser-Use</DialogTitle>
            <DialogDescription>
              Run your recorded test using AI-powered browser automation
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="p-4 bg-muted/50 rounded-lg space-y-2">
              <h4 className="font-medium text-sm">What will happen:</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Your Playwright code will be converted to natural language instructions</li>
                <li>• A browser-use AI agent will execute the test steps</li>
                <li>• You'll see real-time progress and results</li>
              </ul>
            </div>

            <div className="space-y-2">
              <Label htmlFor="execution-headless">Browser Mode</Label>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="execution-headless"
                  className="rounded"
                />
                <label htmlFor="execution-headless" className="text-sm">
                  Run in headless mode (hidden browser)
                </label>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowExecuteDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleExecute}
              disabled={executeMutation.isPending}
            >
              {executeMutation.isPending ? 'Starting...' : 'Execute Test'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Convert Dialog */}
      <Dialog open={showConvertDialog} onOpenChange={setShowConvertDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Convert to Test Case</DialogTitle>
            <DialogDescription>
              Create a new test case from this CodeGen session
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="test_name">Test Name *</Label>
              <Input
                id="test_name"
                value={convertFormData.test_name}
                onChange={(e) => setConvertFormData({ ...convertFormData, test_name: e.target.value })}
                placeholder="e.g., User Login Flow"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="test_description">Description</Label>
              <Textarea
                id="test_description"
                value={convertFormData.test_description}
                onChange={(e) => setConvertFormData({ ...convertFormData, test_description: e.target.value })}
                placeholder="Describe what this test does..."
                rows={3}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="project_id">Project ID *</Label>
              <Input
                id="project_id"
                value={convertFormData.project_id}
                onChange={(e) => setConvertFormData({ ...convertFormData, project_id: e.target.value })}
                placeholder="e.g., my-web-app"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="test_suite">Test Suite</Label>
              <Input
                id="test_suite"
                value={convertFormData.test_suite}
                onChange={(e) => setConvertFormData({ ...convertFormData, test_suite: e.target.value })}
                placeholder="e.g., authentication"
              />
            </div>
          </div>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowConvertDialog(false)}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleConvert}
              disabled={!convertFormData.test_name || !convertFormData.project_id || convertMutation.isPending}
            >
              {convertMutation.isPending ? 'Converting...' : 'Convert'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Execute Dialog */}
      <Dialog open={showExecuteDialog} onOpenChange={setShowExecuteDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Execute Test</DialogTitle>
            <DialogDescription>
              Run the generated test case in your preferred browser
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-muted-foreground">Status</Label>
              <Badge variant={getStatusColor(executionData?.status || 'starting') as any} className="w-fit">
                {executionData?.status || 'starting'}
              </Badge>
            </div>

            {executionData?.status === 'running' && (
              <div className="space-y-2">
                <Label className="text-sm font-medium text-muted-foreground">Target URL</Label>
                <Input 
                  value={executionData.target_url || ''} 
                  readOnly 
                  className="text-sm font-mono"
                />
              </div>
            )}

            {executionData?.status === 'completed' && (
              <div className="space-y-2">
                <Label className="text-sm font-medium text-muted-foreground">Result</Label>
                <Badge variant={executionData.result?.success ? 'default' : 'destructive'} className="w-fit">
                  {executionData.result?.success ? 'Passed' : 'Failed'}
                </Badge>
                {executionData.result?.analysis && (
                  <p className="text-sm text-muted-foreground">{executionData.result.analysis}</p>
                )}
              </div>
            )}

            {executionData?.status === 'failed' && executionData.error && (
              <div className="space-y-2">
                <Label className="text-sm font-medium text-muted-foreground">Error</Label>
                <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-md">
                  <p className="text-sm text-destructive">{executionData.error}</p>
                </div>
              </div>
            )}

            <div className="flex justify-end gap-2">
              {activeExecution && executionData?.status !== 'completed' ? (
                <Button
                  onClick={handleStopExecution}
                  disabled={stopExecutionMutation.isPending}
                >
                  {stopExecutionMutation.isPending ? 'Stopping...' : 'Stop Execution'}
                </Button>
              ) : (
                <Button 
                  onClick={handleExecute}
                  disabled={executeMutation.isPending}
                >
                  {executeMutation.isPending ? 'Executing...' : 'Run Test'}
                </Button>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
