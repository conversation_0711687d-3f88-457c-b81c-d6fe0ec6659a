"use client";

import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus, Play, Square, Download, Trash2, RefreshCw, Monitor, Activity } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';

import {
  startCodegenSession,
  stopCodegenSession,
  listCodegenSessions,
  getCodegenStats,
  getCodegenHealth,
  bulkCleanupCodegenSessions
} from '@/lib/api';

import type {
  CodegenSessionInfo,
  CodegenStatsResponse,
  CodegenHealthResponse,
  PlaywrightCodegenRequest
} from '@/lib/types';

import { NewSessionDialog } from './components/NewSessionDialog';
import { SessionsList } from './components/SessionsList';
import { SessionDetails } from './components/SessionDetails';
import { StatsOverview } from './components/StatsOverview';

export default function CodeGenPage() {
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(null);
  const [showNewSessionDialog, setShowNewSessionDialog] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Queries
  const { data: sessions, isLoading: sessionsLoading } = useQuery({
    queryKey: ['codegen-sessions'],
    queryFn: listCodegenSessions,
    refetchInterval: 5000, // Refresh every 5 seconds
  });

  const { data: stats } = useQuery({
    queryKey: ['codegen-stats'],
    queryFn: getCodegenStats,
    refetchInterval: 10000, // Refresh every 10 seconds
  });

  const { data: health } = useQuery({
    queryKey: ['codegen-health'],
    queryFn: getCodegenHealth,
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Mutations
  const startSessionMutation = useMutation({
    mutationFn: startCodegenSession,
    onSuccess: (data) => {
      toast({
        title: "Recording Started",
        description: `Session ${data.session_id.slice(0, 8)}... started in headless mode - recording will begin automatically!`,
      });
      queryClient.invalidateQueries({ queryKey: ['codegen-sessions'] });
      setSelectedSessionId(data.session_id);
      setShowNewSessionDialog(false);
    },
    onError: (error) => {
      toast({
        title: "Failed to Start Recording",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const stopSessionMutation = useMutation({
    mutationFn: stopCodegenSession,
    onSuccess: () => {
      toast({
        title: "Recording Stopped",
        description: "Session completed - check the generated code in the details panel",
      });
      queryClient.invalidateQueries({ queryKey: ['codegen-sessions'] });
    },
    onError: (error) => {
      toast({
        title: "Error Stopping Recording",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const bulkCleanupMutation = useMutation({
    mutationFn: bulkCleanupCodegenSessions,
    onSuccess: (data) => {
      toast({
        title: "Cleanup Complete",
        description: `Cleaned up ${data.sessions_cleaned} sessions`,
      });
      queryClient.invalidateQueries({ queryKey: ['codegen-sessions'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleStartSession = (request: PlaywrightCodegenRequest) => {
    startSessionMutation.mutate(request);
  };

  const handleStopSession = (sessionId: string) => {
    stopSessionMutation.mutate(sessionId);
  };

  const handleBulkCleanup = () => {
    bulkCleanupMutation.mutate();
  };

  const handleRefresh = () => {
    queryClient.invalidateQueries({ queryKey: ['codegen-sessions'] });
    queryClient.invalidateQueries({ queryKey: ['codegen-stats'] });
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Automated Test Recorder</h1>
          <p className="text-muted-foreground mt-2">
            Record browser interactions in headless mode and automatically generate test code
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefresh}
            disabled={sessionsLoading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleBulkCleanup}
            disabled={bulkCleanupMutation.isPending}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Cleanup
          </Button>
          <Button 
            onClick={() => setShowNewSessionDialog(true)}
            disabled={startSessionMutation.isPending}
          >
            <Plus className="h-4 w-4 mr-2" />
            Start Recording
          </Button>
        </div>
      </div>

      {/* Health Status */}
      {health && (
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium">Service Status</CardTitle>
              <Badge variant={health.playwright_available ? "default" : "destructive"}>
                <Activity className="h-3 w-3 mr-1" />
                {health.playwright_available ? "Ready" : "Unavailable"}
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div>Playwright: {health.playwright_version || "Not available"}</div>
              <div>Active Sessions: {health.active_sessions}</div>
              <div>Total Sessions: {health.total_sessions}</div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* How it Works */}
      <Card className="bg-muted/30">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Monitor className="h-5 w-5" />
            How Automated Recording Works
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center space-y-2">
              <div className="w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center mx-auto text-sm font-medium">
                1
              </div>
              <h3 className="font-medium">Start Recording</h3>
              <p className="text-sm text-muted-foreground">
                Click "Start Recording" to begin automated recording in headless mode
              </p>
            </div>
            <div className="text-center space-y-2">
              <div className="w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center mx-auto text-sm font-medium">
                2
              </div>
              <h3 className="font-medium">Automated Recording</h3>
              <p className="text-sm text-muted-foreground">
                Playwright automatically records interactions based on the provided URL and configuration
              </p>
            </div>
            <div className="text-center space-y-2">
              <div className="w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center mx-auto text-sm font-medium">
                3
              </div>
              <h3 className="font-medium">Generate Code</h3>
              <p className="text-sm text-muted-foreground">
                Stop recording to get test automation code, then convert to QAK test case
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <Tabs defaultValue="sessions" className="space-y-4">
        <TabsList>
          <TabsTrigger value="sessions">
            <Monitor className="h-4 w-4 mr-2" />
            Sessions
          </TabsTrigger>
          <TabsTrigger value="stats">
            <Activity className="h-4 w-4 mr-2" />
            Statistics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="sessions" className="space-y-4">
          {/* Active Session Alert */}
          {sessions?.sessions?.some(s => s.status === 'running') && (
            <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
              <CardContent className="pt-6">
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 rounded-full bg-green-500 animate-pulse" />
                  <div>
                    <p className="font-medium text-green-800 dark:text-green-300">
                      Recording session active
                    </p>
                    <p className="text-sm text-green-600 dark:text-green-400">
                      Headless browser is running - recording interactions automatically
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Sessions List */}
            <div className="space-y-4">
              <SessionsList
                sessions={sessions?.sessions || []}
                isLoading={sessionsLoading}
                selectedSessionId={selectedSessionId}
                onSelectSession={setSelectedSessionId}
                onStopSession={handleStopSession}
                isStoppingSession={stopSessionMutation.isPending}
              />
            </div>

            {/* Session Details */}
            <div className="space-y-4">
              {selectedSessionId ? (
                <SessionDetails
                  sessionId={selectedSessionId}
                  onRefresh={handleRefresh}
                />
              ) : (
                <Card>
                  <CardContent className="flex items-center justify-center h-64 text-muted-foreground">
                    Select a session to view details
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="stats" className="space-y-4">
          <StatsOverview stats={stats} />
        </TabsContent>
      </Tabs>

      {/* New Session Dialog */}
      <NewSessionDialog
        open={showNewSessionDialog}
        onOpenChange={setShowNewSessionDialog}
        onStartSession={handleStartSession}
        isStarting={startSessionMutation.isPending}
      />
    </div>
  );
}
