"""Rutas para integración con Playwright Codegen."""

from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException, BackgroundTasks
from datetime import datetime

from src.API.models import (
    PlaywrightCodegenRequest,
    CodegenSessionInfo,
    CodegenTestCaseRequest,
    CodegenStatsResponse
)
from src.Core.service_container import get_codegen_service

router = APIRouter(prefix="/api/codegen", tags=["codegen"])

@router.post("/start", response_model=CodegenSessionInfo, operation_id="start_codegen_session")
async def start_codegen_session(request: PlaywrightCodegenRequest) -> CodegenSessionInfo:
    """
    Inicia una nueva sesión de Playwright Codegen.
    
    Esta sesión abre un navegador con el Inspector de Playwright para grabar
    interacciones del usuario y generar código automáticamente.
    """
    try:
        session_info = await get_codegen_service().start_session(request)
        return session_info
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error iniciando sesión de codegen: {str(e)}")

@router.get("/session/{session_id}", response_model=CodegenSessionInfo, operation_id="get_codegen_session_status")
async def get_codegen_session(session_id: str) -> CodegenSessionInfo:
    """Obtiene el estado actual de una sesión de codegen."""
    
    session = await get_codegen_service().get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Sesión no encontrada")
    
    return session

@router.post("/session/{session_id}/stop", operation_id="stop_codegen_session")
async def stop_codegen_session(session_id: str) -> Dict[str, str]:
    """Detiene una sesión de codegen activa."""
    
    success = await get_codegen_service().stop_session(session_id)
    if not success:
        raise HTTPException(status_code=404, detail="Sesión no encontrada o ya detenida")
    
    return {"message": "Sesión detenida exitosamente", "session_id": session_id}

@router.get("/session/{session_id}/code", operation_id="get_codegen_generated_code")
async def get_generated_code(session_id: str) -> Dict[str, Any]:
    """Obtiene el código generado de una sesión."""
    
    session = await get_codegen_service().get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Sesión no encontrada")
    
    generated_code = await get_codegen_service().get_generated_code(session_id)
    
    return {
        "session_id": session_id,
        "status": session.status,
        "generated_code": generated_code,
        "target_language": session.target_language,
        "created_at": session.created_at,
        "updated_at": session.updated_at
    }

@router.post("/session/{session_id}/convert", response_model=Dict[str, Any], operation_id="convert_codegen_to_testcase")
async def convert_to_testcase(request: CodegenTestCaseRequest) -> Dict[str, Any]:
    """
    Convierte el código generado por codegen en un caso de prueba QAK.
    
    Adapta el código para integración con el ecosistema QAK y 
    opcionalmente lo guarda en un proyecto específico.
    """
    try:
        testcase_data = await get_codegen_service().convert_to_testcase(request)
        return testcase_data
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error convirtiendo código: {str(e)}")

@router.delete("/session/{session_id}", operation_id="cleanup_codegen_session")
async def cleanup_codegen_session(session_id: str) -> Dict[str, str]:
    """Limpia los recursos de una sesión de codegen."""
    
    success = await get_codegen_service().cleanup_session(session_id)
    if not success:
        raise HTTPException(status_code=404, detail="Sesión no encontrada")
    
    return {"message": "Sesión limpiada exitosamente", "session_id": session_id}

@router.get("/sessions", operation_id="list_codegen_sessions")
async def list_active_sessions() -> Dict[str, Any]:
    """Lista todas las sesiones activas de codegen."""
    
    sessions = []
    for session_id, session in get_codegen_service().active_sessions.items():
        sessions.append({
            "session_id": session_id,
            "status": session.status,
            "target_language": session.target_language,
            "url": session.url,
            "created_at": session.created_at,
            "updated_at": session.updated_at
        })
    
    return {
        "total_sessions": len(sessions),
        "sessions": sessions
    }

@router.get("/stats", response_model=CodegenStatsResponse, operation_id="get_codegen_statistics")
async def get_codegen_stats() -> CodegenStatsResponse:
    """Obtiene estadísticas de uso del servicio de Playwright Codegen."""
    
    return await get_codegen_service().get_stats()

@router.post("/bulk-cleanup", operation_id="bulk_cleanup_codegen_sessions")
async def bulk_cleanup_sessions() -> Dict[str, Any]:
    """Limpia todas las sesiones completadas o fallidas."""
    
    cleanup_count = 0
    sessions_to_cleanup = []
    
    for session_id, session in get_codegen_service().active_sessions.items():
        if session.status in ["completed", "failed", "stopped"]:
            sessions_to_cleanup.append(session_id)
    
    for session_id in sessions_to_cleanup:
        success = await get_codegen_service().cleanup_session(session_id)
        if success:
            cleanup_count += 1
    
    return {
        "message": f"Limpiadas {cleanup_count} sesiones",
        "sessions_cleaned": cleanup_count,
        "sessions_remaining": len(get_codegen_service().active_sessions)
    }

@router.get("/health", operation_id="codegen_health_check")
async def codegen_health_check() -> Dict[str, Any]:
    """Verifica el estado de salud del servicio de codegen."""
    
    try:
        # Verificar si Playwright está instalado
        import subprocess
        result = subprocess.run(
            ["playwright", "--version"], 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        playwright_available = result.returncode == 0
        playwright_version = result.stdout.strip() if playwright_available else None
        
    except Exception:
        playwright_available = False
        playwright_version = None
    
    stats = await get_codegen_service().get_stats()
    
    return {
        "service_status": "healthy",
        "playwright_available": playwright_available,
        "playwright_version": playwright_version,
        "active_sessions": stats.active_sessions,
        "total_sessions": stats.total_sessions,
        "service_uptime": datetime.now().isoformat()
    }

@router.get("/history", operation_id="get_codegen_history")
async def get_codegen_history(limit: int = 50) -> Dict[str, Any]:
    """Obtiene el historial de sesiones de codegen."""
    
    history = get_codegen_service().get_sessions_history(limit)
    
    return {
        "total_sessions": len(history),
        "sessions": history
    }

@router.get("/history/{session_id}", operation_id="get_codegen_history_session")
async def get_codegen_history_session(session_id: str) -> Dict[str, Any]:
    """Obtiene una sesión específica del historial."""
    
    session = get_codegen_service().get_session_from_history(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Sesión no encontrada en el historial")
    
    return session
