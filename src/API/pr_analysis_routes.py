from fastapi import APIRouter, HTTPException
from src.Core.pr_diff_analyzer import PR<PERSON>iffAnalyzer
from typing import Dict

router = APIRouter(
    prefix="/api/pr-analysis",
    tags=["PR Analysis"]
)

pr_analyzer = PRDiffAnalyzer()

@router.get("/{pr_number}")
async def analyze_pr(pr_number: int) -> Dict:
    """
    Analiza un PR de GitHub y devuelve sugerencias de pruebas
    """
    try:
        # Obtener y analizar el diff directamente de GitHub
        pr_analysis = pr_analyzer.get_pr_diff(pr_number)
        
        # Generar sugerencias de pruebas
        test_suggestions = pr_analyzer.suggest_test_cases(pr_analysis)
        
        return {
            "pr_number": pr_number,
            "analysis": pr_analysis,
            "test_suggestions": test_suggestions
        }
    except ValueError as ve:
        # Error de configuración (variables de entorno faltantes)
        raise HTTPException(status_code=500, detail=str(ve))
    except Exception as e:
        # Error al obtener datos de GitHub
        raise HTTPException(status_code=500, detail=str(e))
