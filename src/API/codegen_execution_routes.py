"""Rutas de API para ejecutar tests generados por CodeGen usando browser-use."""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any, Optional, List
from pydantic import BaseModel

from src.Core.service_container import get_executor_service

router = APIRouter(prefix="/api/codegen", tags=["codegen-execution"])

class CodegenExecutionRequest(BaseModel):
    """Request para ejecutar un test de CodeGen."""
    session_id: str
    config_id: Optional[str] = None
    configuration: Optional[Dict[str, Any]] = None

class CodegenExecutionResponse(BaseModel):
    """Response de ejecución de test."""
    execution_id: str
    status: str
    message: str

@router.post("/execute", operation_id="execute_codegen_test")
async def execute_codegen_test(request: CodegenExecutionRequest) -> CodegenExecutionResponse:
    """Ejecuta un test generado por CodeGen usando browser-use."""
    
    try:
        result = await get_executor_service().execute_codegen_test(
            session_id=request.session_id,
            config_id=request.config_id,
            configuration=request.configuration
        )
        
        return CodegenExecutionResponse(**result)
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error ejecutando test: {str(e)}")

@router.get("/execution/{execution_id}", operation_id="get_codegen_execution")
async def get_codegen_execution(execution_id: str) -> Dict[str, Any]:
    """Obtiene información de una ejecución de test."""
    
    execution = get_executor_service().get_execution(execution_id)
    if not execution:
        raise HTTPException(status_code=404, detail="Ejecución no encontrada")
    
    # Convertir datetime a string para JSON
    result = execution.copy()
    if "created_at" in result:
        result["created_at"] = result["created_at"].isoformat()
    if "updated_at" in result:
        result["updated_at"] = result["updated_at"].isoformat()
    if "completed_at" in result:
        result["completed_at"] = result["completed_at"].isoformat()
    
    return result

@router.get("/executions", operation_id="list_codegen_executions")
async def list_codegen_executions() -> Dict[str, Any]:
    """Lista todas las ejecuciones de tests."""
    
    executions = get_executor_service().list_executions()
    
    return {
        "total_executions": len(executions),
        "executions": executions
    }

@router.post("/execution/{execution_id}/stop", operation_id="stop_codegen_execution")
async def stop_codegen_execution(execution_id: str) -> Dict[str, str]:
    """Detiene una ejecución de test activa."""
    
    success = await get_executor_service().stop_execution(execution_id)
    if not success:
        raise HTTPException(status_code=404, detail="Ejecución no encontrada o ya detenida")
    
    return {"message": "Ejecución detenida exitosamente", "execution_id": execution_id}

@router.delete("/execution/{execution_id}", operation_id="cleanup_codegen_execution")
async def cleanup_codegen_execution(execution_id: str) -> Dict[str, str]:
    """Limpia los recursos de una ejecución."""
    
    execution = get_executor_service().get_execution(execution_id)
    if not execution:
        raise HTTPException(status_code=404, detail="Ejecución no encontrada")
    
    # Remover de ejecuciones activas
    if execution_id in get_executor_service().active_executions:
        del get_executor_service().active_executions[execution_id]
    
    return {"message": "Ejecución limpiada exitosamente", "execution_id": execution_id}
