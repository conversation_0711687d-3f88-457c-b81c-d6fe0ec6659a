"""Container de servicios para inyección de dependencias."""

from src.Core.playwright_codegen_service import PlaywrightCodegenService
from src.Core.codegen_executor_service import CodegenExecutorService

# Instancias globales únicas de los servicios
_codegen_service = None
_executor_service = None

def get_codegen_service() -> PlaywrightCodegenService:
    """Obtiene la instancia singleton del servicio de Playwright CodeGen."""
    global _codegen_service
    if _codegen_service is None:
        _codegen_service = PlaywrightCodegenService()
    return _codegen_service

def get_executor_service() -> CodegenExecutorService:
    """Obtiene la instancia singleton del servicio de ejecución."""
    global _executor_service
    if _executor_service is None:
        # Pasar la misma instancia del servicio de codegen
        _executor_service = CodegenExecutorService(get_codegen_service())
    return _executor_service
