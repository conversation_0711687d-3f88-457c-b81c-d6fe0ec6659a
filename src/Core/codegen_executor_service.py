"""Servicio para ejecutar tests generados por Playwright CodeGen usando browser-use."""

import os
import asyncio
import json
import re
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime
import logging
from pathlib import Path
import base64

from browser_use import Browser, Agent as BrowserAgent
from langchain_google_genai import ChatGoogleGenerativeAI
from src.Utilities.browser_helper import create_robust_config
from src.Core.playwright_codegen_service import PlaywrightCodegenService
from src.Utilities.utils import controller

logger = logging.getLogger(__name__)

class CodegenExecutorService:
    """Servicio para ejecutar tests de CodeGen usando browser-use."""
    
    def __init__(self, codegen_service=None):
        """Inicializa el servicio de ejecución.
        
        Args:
            codegen_service: Instancia compartida del servicio de Playwright CodeGen
        """
        print(os.getenv("GOOGLE_API_KEY"))
        self.api_key = os.getenv("GOOGLE_API_KEY")
        if not self.api_key:
            raise ValueError("GOOGLE_API_KEY environment variable is required")
        
        # Validar que la API key tenga el formato correcto
        if len(self.api_key.strip()) < 10:
            raise ValueError("GOOGLE_API_KEY parece inválida (muy corta)")
        
        try:
            # Usar gemini-pro que es el modelo estable más reciente
            self.llm = ChatGoogleGenerativeAI(
                model=os.getenv("LLM_MODEL", "gemini-2.0-flash"),
                api_key=self.api_key
            )
            
            # Validar que el LLM funciona con una prueba simple
            async def test_llm():
                try:
                    response = await self.llm.ainvoke("Test connection")
                    if not response:
                        raise ValueError("No response from LLM")
                    logger.info("LLM connection test successful ✅✅✅✅✅")
                except Exception as e:
                    raise ValueError(f"LLM connection test failed ✅✅✅✅✅: {str(e)}")
            
            # Ejecutar prueba de conexión
            asyncio.create_task(test_llm())
            
        except Exception as e:
            raise ValueError(f"Error initializing LLM: {str(e)}")
        
        # Usar la instancia compartida o crear una nueva si no se proporciona
        if codegen_service is None:
            from src.Core.playwright_codegen_service import PlaywrightCodegenService
            self.codegen_service = PlaywrightCodegenService()
        else:
            self.codegen_service = codegen_service
            
        # Directorios para persistencia (usando ruta absoluta del proyecto)
        root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        self.executions_dir = os.path.join(root_dir, "codegen_sessions", "executions")
        self.screenshots_dir = os.path.join(root_dir, "codegen_sessions", "screenshots")
        
        # Crear directorios si no existen
        os.makedirs(self.executions_dir, exist_ok=True)
        os.makedirs(self.screenshots_dir, exist_ok=True)
        
        # Cargar ejecuciones existentes
        self.active_executions = self._load_executions()
    
    async def execute_codegen_test(
        self, 
        session_id: str,
        config_id: Optional[str] = None,
        configuration: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Ejecuta un test generado por CodeGen usando browser-use.
        
        Args:
            session_id: ID de la sesión de CodeGen
            config_id: ID de configuración de browser (opcional)
            configuration: Configuración personalizada de browser (opcional)
            
        Returns:
            Dict con información de la ejecución
        """
        execution_id = str(uuid.uuid4())
        
        try:
            # Obtener el código generado
            generated_code = await self.codegen_service.get_generated_code(session_id)
            if not generated_code:
                raise ValueError(f"No se encontró código generado para la sesión {session_id}")
            
            # Obtener información de la sesión
            session = await self.codegen_service.get_session(session_id)
            if not session:
                raise ValueError(f"Sesión {session_id} no encontrada")
            
            # Crear información de ejecución
            execution_info = {
                "execution_id": execution_id,
                "session_id": session_id,
                "status": "starting",
                "created_at": datetime.now(),
                "updated_at": datetime.now(),
                "target_url": session.url,
                "generated_code": generated_code,
                "browser_config": configuration or {},
                "history": [],
                "result": None,
                "error": None
            }
            
            self.active_executions[execution_id] = execution_info
            self._save_execution(execution_id)  # Persistir ejecución inicial
            
            # Ejecutar en background
            asyncio.create_task(self._run_execution(execution_id, generated_code, session, config_id, configuration))
            
            logger.info(f"Ejecución de test CodeGen iniciada: {execution_id} para sesión {session_id}")
            return {
                "execution_id": execution_id,
                "status": "starting",
                "message": "Ejecución iniciada exitosamente"
            }
            
        except Exception as e:
            logger.error(f"Error iniciando ejecución para sesión {session_id}: {str(e)}")
            if execution_id in self.active_executions:
                self.active_executions[execution_id]["status"] = "failed"
                self.active_executions[execution_id]["error"] = str(e)
                self.active_executions[execution_id]["updated_at"] = datetime.now()
                self._save_execution(execution_id)  # Persistir error
            raise
    
    async def _run_execution(
        self,
        execution_id: str,
        generated_code: str,
        session,
        config_id: Optional[str],
        configuration: Optional[Dict[str, Any]]
    ):
        """Ejecuta el test en background."""
        execution = self.active_executions[execution_id]
        
        try:
            execution["status"] = "running"
            execution["updated_at"] = datetime.now()
            self._save_execution(execution_id)
            
            # Convertir código de Playwright a instrucciones para browser-use
            instructions = self._convert_playwright_to_instructions(generated_code, session.target_language)
            
            # Crear la configuración base del navegador
            browser_config = create_robust_config()
            
            # Actualizar con la configuración personalizada si existe
            if isinstance(configuration, dict):
                for key, value in configuration.items():
                    setattr(browser_config, key, value)
            
            # Crear directorio para screenshots de esta ejecución
            screenshots_dir = os.path.join(self.screenshots_dir, execution_id)
            os.makedirs(screenshots_dir, exist_ok=True)
            
            # Configurar parámetros del navegador
            browser_params = {
                "headless": getattr(browser_config, "headless", False),
                "user_data_dir": getattr(browser_config, "user_data_dir", None),
                "viewport_width": getattr(browser_config, "viewport_width", None),
                "viewport_height": getattr(browser_config, "viewport_height", None),
                "timezone": getattr(browser_config, "timezone", None),
                "locale": getattr(browser_config, "locale", None),
                "geolocation": getattr(browser_config, "geolocation", None),
                "user_agent": getattr(browser_config, "user_agent", None),
                "proxy": getattr(browser_config, "proxy", None)
            }
            
            # Limpiar None values para evitar errores
            browser_params = {k: v for k, v in browser_params.items() if v is not None}
            
            # Ejecutar con browser-use
            async with Browser(**browser_params) as browser:
                # Crear la tarea para el agente con las instrucciones
                task = {
                    "objective": "Execute test steps",
                    "instructions": instructions,
                    "context": {
                        "session_id": session.session_id,
                        "target_language": session.target_language,
                        "url": session.url
                    }
                }
                
                # Inicializar el agente con todos los parámetros requeridos
                agent = BrowserAgent(
                    browser=browser,
                    llm=self.llm,
                    task=task
                )
                
                # Configurar directorio de screenshots después de la inicialización
                agent.screenshot_dir = screenshots_dir
                
                # Configurar parámetros de ejecución
                agent.max_steps = 50
                agent.timeout = 300
                agent.retry_on_error = True
                
                # Ejecutar instrucciones
                history = await agent.run()
                
                
                # Analizar resultado y guardar screenshots
                result = self._analyze_execution_result(history)
                screenshots = self._extract_screenshots_from_history(history, screenshots_dir)
                
                # Convertir rutas de screenshots a URLs
                screenshot_urls = []
                for screenshot_path in screenshots:
                    # Convertir ruta absoluta a URL relativa
                    relative_path = os.path.relpath(screenshot_path, screenshots_dir)
                    screenshot_url = f"/api/codegen-screenshots/{execution_id}/{relative_path}"
                    screenshot_urls.append(screenshot_url)
                
                # Extraer screenshots del historial como base64
                base64_screenshots = self._extract_base64_screenshots_from_history(history)

                # Combinar screenshots de archivos y base64
                all_screenshots = screenshot_urls + base64_screenshots

                # Analizar resultado
                execution["status"] = "completed"
                execution["history"] = history
                execution["result"] = result
                execution["screenshots"] = all_screenshots
                execution["completed_at"] = datetime.now()
                execution["updated_at"] = datetime.now()
                self._save_execution(execution_id)
            
        except Exception as e:
            logger.error(f"Error en ejecución {execution_id}: {str(e)}")

            # Extraer información detallada del error
            error_info = self._extract_error_info_from_logs(str(e))

            execution["status"] = "failed"
            execution["error"] = str(e)
            execution["error_details"] = error_info
            execution["updated_at"] = datetime.now()

            # Crear un resultado de error más informativo
            execution["result"] = {
                "success": False,
                "error": str(e),
                "analysis": f"❌ Ejecución falló: {error_info.get('message', str(e))}",
                "steps": [{
                    "step_number": 1,
                    "action": "Error de ejecución",
                    "description": f"❌ {error_info.get('message', str(e))}",
                    "status": "error",
                    "timestamp": datetime.now().isoformat(),
                    "details": {
                        "error": str(e),
                        "error_type": error_info.get('type', 'Unknown Error'),
                        "solution": error_info.get('solution', 'Intenta nuevamente o contacta soporte')
                    }
                }],
                "details": {
                    "steps_completed": 0,
                    "steps_failed": 1,
                    "execution_summary": f"Ejecución falló: {error_info.get('type', 'Error desconocido')}",
                    "error_info": error_info
                }
            }

            self._save_execution(execution_id)  # Persistir error

    def _convert_playwright_to_instructions(self, code: str, language: str) -> str:
        """Convierte código de Playwright a instrucciones para browser-use.

        Args:
            code: Código generado por Playwright CodeGen
            language: Lenguaje del código (javascript, python, etc.)

        Returns:
            Instrucciones en lenguaje natural para browser-use
        """
        try:
            # Extraer acciones del código de Playwright
            actions = []
            
            if language in ["javascript", "typescript"]:
                actions = self._extract_actions_from_js(code)
            elif language == "python":
                actions = self._extract_actions_from_python(code)
            else:
                # Fallback: intentar extraer de cualquier formato
                actions = self._extract_actions_generic(code)
            
            # Convertir acciones a instrucciones
            instructions = self._actions_to_instructions(actions)
            
            return instructions
            
        except Exception as e:
            logger.warning(f"Error convirtiendo código a instrucciones: {str(e)}")
            # Fallback: usar el código como está con instrucciones básicas
            return f"""
            Ejecuta las siguientes acciones de prueba basadas en este código:
            
            {code}
            
            Por favor realiza cada acción paso a paso y verifica que se ejecute correctamente.
            """
    
    def _extract_actions_from_js(self, code: str) -> List[Dict[str, Any]]:
        """Extrae acciones de código JavaScript/TypeScript."""
        actions = []
        lines = code.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # page.goto()
            if 'page.goto(' in line:
                url_match = re.search(r"page\.goto\(['\"]([^'\"]+)['\"]", line)
                if url_match:
                    actions.append({
                        "type": "navigate",
                        "url": url_match.group(1)
                    })
            
            # page.click()
            elif 'page.click(' in line or '.click()' in line:
                selector_match = re.search(r"['\"]([^'\"]+)['\"]", line)
                if selector_match:
                    actions.append({
                        "type": "click",
                        "selector": selector_match.group(1)
                    })
            
            # page.fill() or page.type()
            elif 'page.fill(' in line or 'page.type(' in line or '.fill(' in line:
                parts = re.findall(r"['\"]([^'\"]+)['\"]", line)
                if len(parts) >= 2:
                    actions.append({
                        "type": "fill",
                        "selector": parts[0],
                        "text": parts[1]
                    })
            
            # page.selectOption()
            elif 'selectOption(' in line:
                parts = re.findall(r"['\"]([^'\"]+)['\"]", line)
                if len(parts) >= 1:
                    actions.append({
                        "type": "select",
                        "option": parts[-1]
                    })
        
        return actions
    
    def _extract_actions_from_python(self, code: str) -> List[Dict[str, Any]]:
        """Extrae acciones de código Python."""
        actions = []
        lines = code.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # page.goto()
            if 'page.goto(' in line:
                url_match = re.search(r"page\.goto\(['\"]([^'\"]+)['\"]", line)
                if url_match:
                    actions.append({
                        "type": "navigate",
                        "url": url_match.group(1)
                    })
            
            # Similar patterns for Python...
            elif 'page.click(' in line or '.click()' in line:
                selector_match = re.search(r"['\"]([^'\"]+)['\"]", line)
                if selector_match:
                    actions.append({
                        "type": "click",
                        "selector": selector_match.group(1)
                    })
        
        return actions
    
    def _extract_actions_generic(self, code: str) -> List[Dict[str, Any]]:
        """Extrae acciones de código de cualquier formato."""
        actions = []
        
        # Patrones genéricos para encontrar URLs
        url_pattern = r"https?://[^\s'\"]+|['\"]https?://[^'\"]+['\"]"
        urls = re.findall(url_pattern, code)
        
        if urls:
            # Limpiar URL
            url = urls[0].strip('\'"')
            actions.append({
                "type": "navigate",
                "url": url
            })
        
        # Patrones para clicks y fills...
        click_patterns = [
            r"click\(['\"]([^'\"]+)['\"]",
            r"getByRole\(['\"]([^'\"]+)['\"]",
            r"getByText\(['\"]([^'\"]+)['\"]"
        ]
        
        for pattern in click_patterns:
            matches = re.findall(pattern, code, re.IGNORECASE)
            for match in matches:
                actions.append({
                    "type": "click",
                    "selector": match
                })
        
        return actions
    
    def _actions_to_instructions(self, actions: List[Dict[str, Any]]) -> str:
        """Convierte acciones extraídas a instrucciones en lenguaje natural."""
        if not actions:
            return "Realiza las acciones de prueba según el código proporcionado."
        
        instructions = []
        instructions.append("Ejecuta las siguientes acciones paso a paso:")
        
        for i, action in enumerate(actions, 1):
            if action["type"] == "navigate":
                instructions.append(f"{i}. Navega a la URL: {action['url']}")
            elif action["type"] == "click":
                instructions.append(f"{i}. Haz clic en el elemento: {action['selector']}")
            elif action["type"] == "fill":
                instructions.append(f"{i}. Rellena el campo '{action['selector']}' con el texto: {action['text']}")
            elif action["type"] == "select":
                instructions.append(f"{i}. Selecciona la opción: {action['option']}")
        
        instructions.append("\nVerifica que cada acción se ejecute correctamente antes de continuar con la siguiente.")
        
        return "\n".join(instructions)
    
    def _analyze_execution_result(self, history) -> Dict[str, Any]:
        """Analiza el resultado de la ejecución."""
        try:
            # Análisis básico del historial
            # AgentHistoryList tiene un atributo .history que es una lista
            if hasattr(history, 'history'):
                history_items = history.history
                total_steps = len(history_items) if history_items else 0
            elif isinstance(history, list):
                history_items = history
                total_steps = len(history)
            else:
                # Si no podemos determinar el tipo, intentar convertir a string para análisis
                history_items = []
                total_steps = 1 if str(history) else 0
            
            success = total_steps > 0
            
            # Extraer pasos detallados para mostrar en el frontend
            steps = []
            for i, item in enumerate(history_items):
                try:
                    step_info = {
                        "step_number": i + 1,
                        "action": "Unknown",
                        "description": "No description available",
                        "status": "completed",
                        "timestamp": None,
                        "details": {}
                    }

                    # Extraer información del paso desde el objeto AgentHistory de browser-use
                    if hasattr(item, 'model_output'):
                        model_output = item.model_output
                        if hasattr(model_output, 'current_state'):
                            current_state = model_output.current_state

                            # Extraer información del AgentBrain
                            next_goal = getattr(current_state, 'next_goal', '')
                            memory = getattr(current_state, 'memory', '')
                            evaluation = getattr(current_state, 'evaluation_previous_goal', '')

                            if next_goal:
                                step_info["action"] = self._clean_action_text(next_goal)
                            elif memory:
                                step_info["action"] = self._clean_action_text(memory.split('.')[0] if '.' in memory else memory)

                            # Combinar información para la descripción
                            description_parts = []
                            if evaluation and evaluation != "Unknown":
                                description_parts.append(f"Resultado anterior: {evaluation}")
                            if memory:
                                description_parts.append(f"Contexto: {memory}")

                            if description_parts:
                                step_info["description"] = " | ".join(description_parts)

                            # Agregar detalles adicionales
                            step_info["details"] = {
                                "evaluation": evaluation,
                                "memory": memory,
                                "next_goal": next_goal
                            }

                    # Extraer información del resultado si está disponible
                    if hasattr(item, 'result'):
                        result = item.result
                        if isinstance(result, list) and len(result) > 0:
                            result_item = result[0]
                            if hasattr(result_item, 'extracted_content'):
                                extracted_content = result_item.extracted_content
                                if extracted_content:
                                    # Si no tenemos acción, usar el contenido extraído
                                    if step_info["action"] == "Unknown":
                                        step_info["action"] = extracted_content
                                    # Agregar el contenido extraído a la descripción
                                    if step_info["description"] == "No description available":
                                        step_info["description"] = extracted_content
                                    else:
                                        step_info["description"] += f" | Result: {extracted_content}"

                            # Determinar el estado basado en el resultado
                            error_info = None
                            if hasattr(result_item, 'error') and result_item.error:
                                step_info["status"] = "error"
                                error_info = str(result_item.error)
                            elif hasattr(result_item, 'success') and result_item.success is False:
                                step_info["status"] = "error"
                                error_info = "Acción falló sin mensaje de error específico"
                            else:
                                step_info["status"] = "completed"

                            # Agregar información de error a los detalles
                            if error_info:
                                step_info["details"]["error"] = error_info
                                step_info["description"] = f"❌ Error: {error_info}"
                            elif hasattr(result_item, 'extracted_content') and result_item.extracted_content:
                                step_info["details"]["result"] = result_item.extracted_content

                    # Extraer timestamp de metadata si está disponible
                    if hasattr(item, 'metadata'):
                        metadata = item.metadata
                        if hasattr(metadata, 'step_start_time'):
                            step_start_time = metadata.step_start_time
                            if step_start_time:
                                # Convertir timestamp Unix a ISO format
                                import datetime
                                try:
                                    dt = datetime.datetime.fromtimestamp(step_start_time)
                                    step_info["timestamp"] = dt.isoformat()
                                except:
                                    pass

                    # Fallback: si no tenemos información específica, intentar extraer de dict si es posible
                    elif isinstance(item, dict):
                        # Código de fallback para compatibilidad con JSON estático
                        model_output = item.get('model_output', {})
                        if isinstance(model_output, dict):
                            current_state = model_output.get('current_state', {})
                            if isinstance(current_state, dict):
                                next_goal = current_state.get('next_goal', '')
                                memory = current_state.get('memory', '')
                                evaluation = current_state.get('evaluation_previous_goal', '')

                                if next_goal:
                                    step_info["action"] = next_goal
                                elif memory:
                                    step_info["action"] = memory.split('.')[0] if '.' in memory else memory

                                description_parts = []
                                if evaluation and evaluation != "Unknown":
                                    description_parts.append(f"Previous: {evaluation}")
                                if memory:
                                    description_parts.append(f"Context: {memory}")

                                if description_parts:
                                    step_info["description"] = " | ".join(description_parts)

                    steps.append(step_info)

                except Exception as e:
                    logger.warning(f"Error procesando paso {i}: {str(e)}")
                    steps.append({
                        "step_number": i + 1,
                        "action": "Error procesando paso",
                        "description": f"Error interno: {str(e)}",
                        "status": "error",
                        "timestamp": None,
                        "details": {
                            "error_type": type(e).__name__,
                            "error_message": str(e),
                            "step_index": i
                        }
                    })
            
            # Calcular estadísticas de éxito
            completed_steps = len([s for s in steps if s["status"] == "completed"])
            error_steps = len([s for s in steps if s["status"] == "error"])

            # Determinar éxito general basado en los pasos
            overall_success = error_steps == 0 and completed_steps > 0

            # Generar análisis más detallado
            if overall_success:
                analysis = f"✅ Ejecución completada exitosamente - {completed_steps} pasos ejecutados"
            elif error_steps > 0:
                analysis = f"❌ Ejecución completada con errores - {completed_steps} exitosos, {error_steps} fallidos"
            else:
                analysis = "⚠️ No se ejecutaron pasos válidos"

            return {
                "success": overall_success,
                "total_steps": total_steps,
                "analysis": analysis,
                "steps": steps,
                "details": {
                    "steps_completed": completed_steps,
                    "steps_failed": error_steps,
                    "execution_summary": f"Se ejecutaron {total_steps} pasos ({completed_steps} exitosos, {error_steps} fallidos)",
                    "history_type": str(type(history).__name__)
                }
            }
        except Exception as e:
            logger.error(f"Error analizando resultado de ejecución: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "analysis": "Error analizando resultado",
                "steps": [],
                "details": {}
            }
    
    def get_execution(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """Obtiene información de una ejecución."""
        return self.active_executions.get(execution_id)
    
    def list_executions(self) -> List[Dict[str, Any]]:
        """Lista todas las ejecuciones activas."""
        return [
            {
                "execution_id": exec_id,
                "session_id": exec_info["session_id"],
                "status": exec_info["status"],
                "created_at": exec_info["created_at"].isoformat(),
                "updated_at": exec_info["updated_at"].isoformat(),
                "target_url": exec_info.get("target_url")
            }
            for exec_id, exec_info in self.active_executions.items()
        ]

    
    async def stop_execution(self, execution_id: str) -> bool:
        """Detiene una ejecución activa."""
        if execution_id not in self.active_executions:
            return False
        
        execution = self.active_executions[execution_id]
        if execution["status"] in ["completed", "failed", "stopped"]:
            return True
        
        execution["status"] = "stopped"
        execution["updated_at"] = datetime.now()
        self._save_execution(execution_id)  # Persistir detención
        
        logger.info(f"Ejecución {execution_id} detenida")
        return True

    def _extract_base64_screenshots_from_history(self, history) -> List[str]:
        """Extrae screenshots en formato base64 del historial de browser-use.

        Args:
            history: Historial de ejecución de browser-use

        Returns:
            Lista de screenshots en formato data URL (data:image/png;base64,...)
        """
        screenshots = []

        try:
            # Obtener los elementos del historial
            if hasattr(history, 'history'):
                history_items = history.history
                logger.info(f"DEBUG Screenshots: history.history tiene {len(history_items)} elementos")
            elif isinstance(history, list):
                history_items = history
                logger.info(f"DEBUG Screenshots: history es lista con {len(history_items)} elementos")
            else:
                logger.warning("No se puede acceder al historial para extraer screenshots base64")
                return screenshots

            for i, item in enumerate(history_items):
                logger.info(f"DEBUG Screenshots: Procesando step {i + 1}, tipo: {type(item)}")
                try:
                    # Buscar screenshots en objetos AgentHistory
                    if hasattr(item, 'result') and item.result:
                        logger.info(f"Step {i + 1}: Tiene result con {len(item.result)} elementos")
                        for j, result_item in enumerate(item.result):
                            if hasattr(result_item, 'screenshot') and result_item.screenshot:
                                screenshot_b64 = result_item.screenshot
                                logger.debug(f"Step {i + 1}, result {j}: Screenshot encontrado, longitud: {len(screenshot_b64)}")
                                if screenshot_b64 and len(screenshot_b64) > 100:  # Verificar que no esté vacío
                                    # Convertir a data URL si no lo es ya
                                    if not screenshot_b64.startswith('data:image'):
                                        screenshot_data_url = f"data:image/png;base64,{screenshot_b64}"
                                    else:
                                        screenshot_data_url = screenshot_b64
                                    screenshots.append(screenshot_data_url)
                                    logger.info(f"Screenshot extraído del step {i + 1}")
                            else:
                                logger.debug(f"Step {i + 1}, result {j}: No tiene screenshot o está vacío")

                    # Fallback: buscar en diccionarios si es JSON estático
                    elif isinstance(item, dict):
                        # Buscar screenshot directamente en el step
                        if 'screenshot' in item:
                            screenshot_b64 = item['screenshot']
                            if screenshot_b64 and len(screenshot_b64) > 100:
                                # Convertir a data URL si no lo es ya
                                if not screenshot_b64.startswith('data:image'):
                                    screenshot_data_url = f"data:image/png;base64,{screenshot_b64}"
                                else:
                                    screenshot_data_url = screenshot_b64
                                screenshots.append(screenshot_data_url)
                                logger.info(f"Screenshot extraído del step {i + 1} (JSON)")

                        # Buscar screenshot en el campo state
                        elif 'state' in item and isinstance(item['state'], dict) and 'screenshot' in item['state']:
                            screenshot_b64 = item['state']['screenshot']
                            if screenshot_b64 and len(screenshot_b64) > 100:
                                # Convertir a data URL si no lo es ya
                                if not screenshot_b64.startswith('data:image'):
                                    screenshot_data_url = f"data:image/png;base64,{screenshot_b64}"
                                else:
                                    screenshot_data_url = screenshot_b64
                                screenshots.append(screenshot_data_url)
                                logger.info(f"Screenshot extraído del step {i + 1} (JSON)")

                        # También buscar en result como fallback
                        result = item.get('result', [])
                        if isinstance(result, list):
                            for j, result_item in enumerate(result):
                                if isinstance(result_item, dict) and 'screenshot' in result_item:
                                    screenshot_b64 = result_item['screenshot']
                                    logger.info(f"Step {i + 1}, result {j}: Screenshot encontrado en result, longitud: {len(screenshot_b64) if screenshot_b64 else 0}")
                                    if screenshot_b64 and len(screenshot_b64) > 100:
                                        if not screenshot_b64.startswith('data:image'):
                                            screenshot_data_url = f"data:image/png;base64,{screenshot_b64}"
                                        else:
                                            screenshot_data_url = screenshot_b64
                                        screenshots.append(screenshot_data_url)
                                        logger.info(f"Screenshot extraído del step {i + 1} (JSON) - desde result")
                    else:
                        logger.debug(f"Step {i + 1}: No es dict")

                except Exception as e:
                    logger.warning(f"Error extrayendo screenshot del step {i}: {str(e)}")
                    continue

            logger.info(f"Extraídos {len(screenshots)} screenshots base64 del historial")
            return screenshots

        except Exception as e:
            logger.error(f"Error extrayendo screenshots base64 del historial: {str(e)}")
            return screenshots

    def update_execution_with_screenshots_from_json(self, execution_id: str, json_file_path: str):
        """Actualiza una ejecución existente con screenshots extraídos de un archivo JSON.

        Args:
            execution_id: ID de la ejecución a actualizar
            json_file_path: Ruta al archivo JSON con el historial
        """
        try:
            if execution_id not in self.active_executions:
                logger.warning(f"Ejecución {execution_id} no encontrada")
                return

            with open(json_file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)

            # Extraer screenshots del historial JSON
            history_data = json_data.get("history", {})
            logger.info(f"DEBUG: history_data keys: {list(history_data.keys()) if isinstance(history_data, dict) else 'Not a dict'}")

            if isinstance(history_data, dict) and "history" in history_data:
                history_list = history_data["history"]
                logger.info(f"DEBUG: history_list length: {len(history_list) if isinstance(history_list, list) else 'Not a list'}")

                if isinstance(history_list, list) and len(history_list) > 0:
                    logger.info(f"DEBUG: First history item keys: {list(history_list[0].keys()) if isinstance(history_list[0], dict) else 'Not a dict'}")

                    # Buscar screenshots en el primer elemento para debug
                    first_item = history_list[0]
                    if isinstance(first_item, dict):
                        result = first_item.get('result', [])
                        logger.info(f"DEBUG: First item result length: {len(result) if isinstance(result, list) else 'Not a list'}")
                        if isinstance(result, list) and len(result) > 0:
                            result_item = result[0]
                            logger.info(f"DEBUG: First result item keys: {list(result_item.keys()) if isinstance(result_item, dict) else 'Not a dict'}")
                            if isinstance(result_item, dict) and 'screenshot' in result_item:
                                screenshot_len = len(result_item['screenshot']) if result_item['screenshot'] else 0
                                logger.info(f"DEBUG: Screenshot found, length: {screenshot_len}")

                # Simular el objeto history con la estructura esperada
                class MockHistory:
                    def __init__(self, history_list):
                        self.history = history_list

                mock_history = MockHistory(history_data["history"])
                base64_screenshots = self._extract_base64_screenshots_from_history(mock_history)

                # Actualizar la ejecución con los screenshots
                execution = self.active_executions[execution_id]
                existing_screenshots = execution.get("screenshots", [])

                # Combinar screenshots existentes con los nuevos
                all_screenshots = existing_screenshots + base64_screenshots
                execution["screenshots"] = all_screenshots

                logger.info(f"Ejecución {execution_id} actualizada con {len(base64_screenshots)} screenshots del JSON")
            else:
                logger.warning(f"No se encontró historial válido en el JSON")

        except Exception as e:
            logger.error(f"Error actualizando ejecución con screenshots del JSON: {str(e)}")

    def create_test_execution_from_json(self, json_file_path: str, session_id: Optional[str] = None) -> str:
        """Crea una ejecución de prueba desde un archivo JSON existente."""
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)

            execution_id = json_data.get("execution_id", str(uuid.uuid4()))

            # Crear información de ejecución basada en el JSON
            final_session_id = session_id if session_id is not None else json_data.get("session_id", "test-session")
            execution_info = {
                "execution_id": execution_id,
                "session_id": final_session_id,
                "status": "completed",  # Siempre completed para ejecuciones de prueba
                "created_at": datetime.fromisoformat(json_data.get("created_at", datetime.now().isoformat())),
                "updated_at": datetime.fromisoformat(json_data.get("updated_at", datetime.now().isoformat())),
                "completed_at": datetime.fromisoformat(json_data.get("completed_at", datetime.now().isoformat())) if json_data.get("completed_at") else None,
                "target_url": json_data.get("target_url"),
                "generated_code": json_data.get("generated_code", ""),
                "browser_config": json_data.get("browser_config", {}),
                "history": json_data.get("history", {}),
                "screenshots": [],  # Inicializar vacío, se llenará después
                "result": None,
                "error": json_data.get("error")
            }

            # Analizar el historial para generar los steps
            history_data = json_data.get("history", {})
            if isinstance(history_data, dict) and "history" in history_data:
                # Simular el objeto history con la estructura esperada
                class MockHistory:
                    def __init__(self, history_list):
                        self.history = history_list

                mock_history = MockHistory(history_data["history"])
                execution_info["result"] = self._analyze_execution_result(mock_history)

                # Extraer screenshots del historial
                base64_screenshots = self._extract_base64_screenshots_from_history(mock_history)
                execution_info["screenshots"] = base64_screenshots

            # Agregar a ejecuciones activas
            self.active_executions[execution_id] = execution_info

            logger.info(f"Ejecución de prueba creada desde JSON: {execution_id} con {len(execution_info['screenshots'])} screenshots")
            return execution_id

        except Exception as e:
            logger.error(f"Error creando ejecución de prueba desde JSON: {str(e)}")
            raise
    
    def _extract_screenshots_from_history(self, history, screenshots_dir: str) -> List[str]:
        """Extrae screenshots del historial de browser-use y los guarda en el directorio especificado.
        
        Args:
            history: Historial de ejecución de browser-use
            screenshots_dir: Directorio donde guardar los screenshots
            
        Returns:
            Lista de rutas de los screenshots guardados
        """
        screenshots = []
        
        try:
            # Browser-use puede tener screenshots en diferentes formatos
            # Vamos a intentar extraer de las diferentes formas posibles
            
            if hasattr(history, 'history'):
                history_items = history.history
            elif isinstance(history, list):
                history_items = history
            else:
                # Si no podemos acceder al historial, crear screenshot del estado final
                logger.warning("No se puede acceder al historial para extraer screenshots")
                return screenshots
            
            # Recorrer los items del historial
            for i, item in enumerate(history_items):
                try:
                    # Browser-use puede almacenar screenshots en diferentes campos
                    screenshot_data = None
                    
                    # Intentar diferentes formas de acceder a los screenshots
                    if hasattr(item, 'screenshot'):
                        screenshot_data = item.screenshot
                    elif isinstance(item, dict):
                        screenshot_data = item.get('screenshot') or item.get('image') or item.get('screenshot_data')
                    
                    if screenshot_data:
                        # Guardar el screenshot
                        screenshot_path = os.path.join(screenshots_dir, f"step_{i+1}.png")
                        
                        # Si es base64, decodificar y guardar
                        if isinstance(screenshot_data, str):
                            # Remover prefijo data:image si existe
                            if screenshot_data.startswith('data:image'):
                                screenshot_data = screenshot_data.split(',')[1]
                            
                            with open(screenshot_path, "wb") as f:
                                f.write(base64.b64decode(screenshot_data))
                        elif isinstance(screenshot_data, bytes):
                            with open(screenshot_path, "wb") as f:
                                f.write(screenshot_data)
                        else:
                            continue
                        
                        screenshots.append(screenshot_path)
                        logger.debug(f"Screenshot guardado: {screenshot_path}")
                        
                except Exception as e:
                    logger.warning(f"Error procesando screenshot {i}: {str(e)}")
                    continue
            
            # Si no encontramos screenshots en el historial, intentar crear uno del estado final
            if not screenshots:
                logger.info("No se encontraron screenshots en el historial, esto es normal para browser-use")
                # Browser-use no siempre genera screenshots automáticamente
                # Los screenshots son opcionales y dependen de la configuración
            
        except Exception as e:
            logger.error(f"Error extrayendo screenshots del historial: {str(e)}")
        
        return screenshots
    
    def _load_executions(self) -> Dict[str, Any]:
        """Carga las ejecuciones existentes desde el sistema de archivos."""
        executions = {}
        
        try:
            # Cargar desde el archivo central
            central_file = os.path.join(self.executions_dir, "executions.json")
            if os.path.exists(central_file):
                with open(central_file, "r") as f:
                    executions = json.load(f)
                    
                    # Convertir strings de fecha a datetime
                    for exec_id, exec_info in executions.items():
                        for date_field in ["created_at", "updated_at", "completed_at"]:
                            if date_field in exec_info and exec_info[date_field]:
                                exec_info[date_field] = datetime.fromisoformat(exec_info[date_field])
            
            # Cargar ejecuciones individuales y hacer merge
            for file in Path(self.executions_dir).glob("execution_*.json"):
                try:
                    with open(file, "r") as f:
                        execution = json.load(f)
                        if "execution_id" in execution:
                            exec_id = execution["execution_id"]
                            
                            # Convertir strings de fecha a datetime
                            for date_field in ["created_at", "updated_at", "completed_at"]:
                                if date_field in execution and execution[date_field]:
                                    execution[date_field] = datetime.fromisoformat(execution[date_field])
                                    
                            executions[exec_id] = execution
                except Exception as e:
                    logger.warning(f"Error loading execution file {file}: {str(e)}")
                    
            # Limpiar ejecuciones antiguas (>30 días)
            now = datetime.now()
            executions = {
                exec_id: exec_info 
                for exec_id, exec_info in executions.items()
                if (now - exec_info["created_at"]).days <= 30
            }
            
        except Exception as e:
            logger.error(f"Error loading executions: {str(e)}")
        
        return executions
    
    def _save_execution(self, execution_id: str):
        """Guarda una ejecución en el sistema de archivos."""
        try:
            execution = self.active_executions[execution_id]
            
            # Convertir datetimes a strings para JSON
            execution_json = execution.copy()
            for date_field in ["created_at", "updated_at", "completed_at"]:
                if date_field in execution_json and execution_json[date_field]:
                    execution_json[date_field] = execution_json[date_field].isoformat()
            
            # Guardar archivo individual
            file_path = os.path.join(self.executions_dir, f"execution_{execution_id}.json")
            with open(file_path, "w") as f:
                json.dump(execution_json, f, indent=2)
            
            # Actualizar archivo central
            central_file = os.path.join(self.executions_dir, "executions.json")
            all_executions = {}
            
            if os.path.exists(central_file):
                with open(central_file, "r") as f:
                    all_executions = json.load(f)
            
            all_executions[execution_id] = execution_json
            
            with open(central_file, "w") as f:
                json.dump(all_executions, f, indent=2)
                
        except Exception as e:
            logger.error(f"Error saving execution {execution_id}: {str(e)}")

    def _clean_action_text(self, text: str) -> str:
        """Limpia y formatea el texto de la acción para que sea más legible."""
        if not text:
            return "Acción desconocida"

        # Remover prefijos comunes y limpiar texto
        text = text.strip()

        # Mapear acciones comunes a texto más claro en español
        action_mappings = {
            "click_element_by_index": "Hacer clic en elemento",
            "input_text": "Escribir texto",
            "navigate": "Navegar a página",
            "scroll": "Desplazar página",
            "wait": "Esperar",
            "take_screenshot": "Capturar pantalla",
            "fill": "Rellenar campo",
            "select": "Seleccionar opción",
            "press": "Presionar tecla",
            "hover": "Pasar cursor sobre elemento"
        }

        # Buscar mapeos conocidos
        text_lower = text.lower()
        for key, value in action_mappings.items():
            if key in text_lower:
                return value

        # Extraer información útil de textos largos
        if "click" in text_lower:
            if "button" in text_lower:
                return "Hacer clic en botón"
            elif "link" in text_lower:
                return "Hacer clic en enlace"
            else:
                return "Hacer clic en elemento"
        elif "input" in text_lower or "type" in text_lower:
            return "Escribir texto"
        elif "navigate" in text_lower or "goto" in text_lower:
            return "Navegar a página"

        # Si es muy largo, truncar pero mantener información útil
        if len(text) > 80:
            text = text[:77] + "..."

        return text

    def _extract_error_info_from_logs(self, logs: str) -> Dict[str, str]:
        """Extrae información de error de los logs de browser-use."""
        error_info = {}

        try:
            # Buscar patrones comunes de error en los logs
            lines = logs.split('\n')

            for i, line in enumerate(lines):
                # Buscar errores de cuota de API
                if "ResourceExhausted" in line and "quota" in line.lower():
                    error_info["type"] = "API Quota Exceeded"
                    error_info["message"] = "Se excedió la cuota de la API de Google Gemini"
                    error_info["solution"] = "Espera unos minutos antes de intentar nuevamente o verifica tu plan de facturación"
                    break

                # Buscar errores de timeout
                elif "timeout" in line.lower() or "timed out" in line.lower():
                    error_info["type"] = "Timeout Error"
                    error_info["message"] = "La operación tardó demasiado tiempo en completarse"
                    error_info["solution"] = "Intenta nuevamente o verifica la conectividad de red"
                    break

                # Buscar errores de navegación
                elif "navigation" in line.lower() and ("failed" in line.lower() or "error" in line.lower()):
                    error_info["type"] = "Navigation Error"
                    error_info["message"] = "Error al navegar a la página especificada"
                    error_info["solution"] = "Verifica que la URL sea correcta y accesible"
                    break

                # Buscar errores de elementos no encontrados
                elif "element" in line.lower() and ("not found" in line.lower() or "no encontrado" in line.lower()):
                    error_info["type"] = "Element Not Found"
                    error_info["message"] = "No se pudo encontrar el elemento especificado en la página"
                    error_info["solution"] = "Verifica que el elemento exista y sea visible en la página"
                    break

                # Buscar errores de credenciales
                elif "credentials" in line.lower() or "login" in line.lower() and "failed" in line.lower():
                    error_info["type"] = "Authentication Error"
                    error_info["message"] = "Error de autenticación o credenciales incorrectas"
                    error_info["solution"] = "Verifica que las credenciales sean correctas"
                    break

                # Buscar errores generales de browser-use
                elif "❌" in line or "ERROR" in line:
                    # Extraer el mensaje de error
                    if ":" in line:
                        parts = line.split(":", 1)
                        if len(parts) > 1:
                            error_info["type"] = "Execution Error"
                            error_info["message"] = parts[1].strip()
                            error_info["solution"] = "Revisa los pasos anteriores y verifica la configuración"
                            break

            # Si no se encontró información específica, proporcionar información genérica
            if not error_info:
                error_info = {
                    "type": "Unknown Error",
                    "message": "Se produjo un error durante la ejecución",
                    "solution": "Revisa los logs para más detalles o intenta nuevamente"
                }

        except Exception as e:
            logger.warning(f"Error extrayendo información de error de logs: {str(e)}")
            error_info = {
                "type": "Log Processing Error",
                "message": "No se pudo procesar la información de error",
                "solution": "Contacta al soporte técnico"
            }

        return error_info
