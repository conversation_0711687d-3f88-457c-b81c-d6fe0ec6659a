"""Servicio para ejecutar tests generados por Playwright CodeGen usando browser-use."""

import os
import asyncio
import json
import re
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime
import logging
from pathlib import Path
import base64

from browser_use import Browser, Agent as BrowserAgent
from langchain_google_genai import ChatGoogleGenerativeAI
from src.Utilities.browser_helper import create_robust_config
from src.Core.playwright_codegen_service import PlaywrightCodegenService
from src.Utilities.utils import controller

logger = logging.getLogger(__name__)

class CodegenExecutorService:
    """Servicio para ejecutar tests de CodeGen usando browser-use."""
    
    def __init__(self, codegen_service=None):
        """Inicializa el servicio de ejecución.
        
        Args:
            codegen_service: Instancia compartida del servicio de Playwright CodeGen
        """
        self.api_key = os.getenv("GOOGLE_API_KEY")
        if not self.api_key:
            raise ValueError("GOOGLE_API_KEY environment variable is required")
        
        self.llm = ChatGoogleGenerativeAI(
            model=os.getenv("LLM_MODEL", "gemini-2.0-flash"),
            api_key=self.api_key
        )
        
        # Usar la instancia compartida o crear una nueva si no se proporciona
        if codegen_service is None:
            from src.Core.playwright_codegen_service import PlaywrightCodegenService
            self.codegen_service = PlaywrightCodegenService()
        else:
            self.codegen_service = codegen_service
            
        self.active_executions: Dict[str, Dict[str, Any]] = {}
        
        # Directorio base para screenshots
        import tempfile
        self.base_temp_dir = os.path.join(tempfile.gettempdir(), "qak_codegen")
        os.makedirs(self.base_temp_dir, exist_ok=True)
        
    async def execute_codegen_test(
        self, 
        session_id: str,
        config_id: Optional[str] = None,
        configuration: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Ejecuta un test generado por CodeGen usando browser-use.
        
        Args:
            session_id: ID de la sesión de CodeGen
            config_id: ID de configuración de browser (opcional)
            configuration: Configuración personalizada de browser (opcional)
            
        Returns:
            Dict con información de la ejecución
        """
        execution_id = str(uuid.uuid4())
        
        try:
            # Obtener el código generado
            generated_code = await self.codegen_service.get_generated_code(session_id)
            if not generated_code:
                raise ValueError(f"No se encontró código generado para la sesión {session_id}")
            
            # Obtener información de la sesión
            session = await self.codegen_service.get_session(session_id)
            if not session:
                raise ValueError(f"Sesión {session_id} no encontrada")
            
            # Crear información de ejecución
            execution_info = {
                "execution_id": execution_id,
                "session_id": session_id,
                "status": "starting",
                "created_at": datetime.now(),
                "updated_at": datetime.now(),
                "target_url": session.url,
                "generated_code": generated_code,
                "browser_config": configuration or {},
                "history": [],
                "result": None,
                "error": None
            }
            
            self.active_executions[execution_id] = execution_info
            
            # Ejecutar en background
            asyncio.create_task(self._run_execution(execution_id, generated_code, session, config_id, configuration))
            
            logger.info(f"Ejecución de test CodeGen iniciada: {execution_id} para sesión {session_id}")
            return {
                "execution_id": execution_id,
                "status": "starting",
                "message": "Ejecución iniciada exitosamente"
            }
            
        except Exception as e:
            logger.error(f"Error iniciando ejecución para sesión {session_id}: {str(e)}")
            if execution_id in self.active_executions:
                self.active_executions[execution_id]["status"] = "failed"
                self.active_executions[execution_id]["error"] = str(e)
            raise
    
    async def _run_execution(
        self,
        execution_id: str,
        generated_code: str,
        session,
        config_id: Optional[str],
        configuration: Optional[Dict[str, Any]]
    ):
        """Ejecuta el test en background."""
        execution = self.active_executions[execution_id]
        
        try:
            execution["status"] = "running"
            execution["updated_at"] = datetime.now()
            
            # Convertir código de Playwright a instrucciones para browser-use
            instructions = self._convert_playwright_to_instructions(generated_code, session.target_language)
            
            # Configurar browser
            if isinstance(configuration, dict):
                browser_config = configuration
            else:
                # Si no se proporciona configuración, usar configuración robusta por defecto
                default_config = create_robust_config()
                browser_config = {
                    "headless": default_config.headless,
                    "user_data_dir": default_config.user_data_dir,
                }
            
            # Crear directorio para screenshots de esta ejecución
            screenshots_dir = os.path.join(self.base_temp_dir, "screenshots", execution_id)
            os.makedirs(screenshots_dir, exist_ok=True)
            
            # Ejecutar con browser-use
            async with Browser(
                headless=browser_config.get("headless", False),
                user_data_dir=browser_config.get("user_data_dir"),
            ) as browser:
                agent = BrowserAgent(
                    task=instructions,
                    llm=self.llm,
                    browser=browser,
                    controller=controller
                )
                
                # Ejecutar el test
                history = await agent.run()
                
                # Extraer screenshots del historial si están disponibles
                screenshots = self._extract_screenshots_from_history(history, screenshots_dir)
                
                # Convertir rutas de screenshots a URLs
                screenshot_urls = []
                for screenshot_path in screenshots:
                    # Convertir ruta absoluta a URL relativa
                    relative_path = os.path.relpath(screenshot_path, screenshots_dir)
                    screenshot_url = f"/api/codegen-screenshots/{execution_id}/{relative_path}"
                    screenshot_urls.append(screenshot_url)
                
                # Analizar resultado
                execution["status"] = "completed"
                execution["history"] = history
                execution["result"] = self._analyze_execution_result(history)
                execution["screenshots"] = screenshot_urls
                execution["completed_at"] = datetime.now()
                execution["updated_at"] = datetime.now()
                
                logger.info(f"Ejecución {execution_id} completada exitosamente con {len(screenshot_urls)} screenshots")
                
        except Exception as e:
            logger.error(f"Error en ejecución {execution_id}: {str(e)}")
            execution["status"] = "failed"
            execution["error"] = str(e)
            execution["updated_at"] = datetime.now()
    
    def _convert_playwright_to_instructions(self, code: str, language: str) -> str:
        """Convierte código de Playwright a instrucciones para browser-use.
        
        Args:
            code: Código generado por Playwright
            language: Lenguaje del código (javascript, python, etc.)
            
        Returns:
            Instrucciones en lenguaje natural para browser-use
        """
        try:
            # Extraer acciones del código de Playwright
            actions = []
            
            if language in ["javascript", "typescript"]:
                actions = self._extract_actions_from_js(code)
            elif language == "python":
                actions = self._extract_actions_from_python(code)
            else:
                # Fallback: intentar extraer de cualquier formato
                actions = self._extract_actions_generic(code)
            
            # Convertir acciones a instrucciones
            instructions = self._actions_to_instructions(actions)
            
            return instructions
            
        except Exception as e:
            logger.warning(f"Error convirtiendo código a instrucciones: {str(e)}")
            # Fallback: usar el código como está con instrucciones básicas
            return f"""
            Ejecuta las siguientes acciones de prueba basadas en este código:
            
            {code}
            
            Por favor realiza cada acción paso a paso y verifica que se ejecute correctamente.
            """
    
    def _extract_actions_from_js(self, code: str) -> List[Dict[str, Any]]:
        """Extrae acciones de código JavaScript/TypeScript."""
        actions = []
        lines = code.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # page.goto()
            if 'page.goto(' in line:
                url_match = re.search(r"page\.goto\(['\"]([^'\"]+)['\"]", line)
                if url_match:
                    actions.append({
                        "type": "navigate",
                        "url": url_match.group(1)
                    })
            
            # page.click()
            elif 'page.click(' in line or '.click()' in line:
                selector_match = re.search(r"['\"]([^'\"]+)['\"]", line)
                if selector_match:
                    actions.append({
                        "type": "click",
                        "selector": selector_match.group(1)
                    })
            
            # page.fill() or page.type()
            elif 'page.fill(' in line or 'page.type(' in line or '.fill(' in line:
                parts = re.findall(r"['\"]([^'\"]+)['\"]", line)
                if len(parts) >= 2:
                    actions.append({
                        "type": "fill",
                        "selector": parts[0],
                        "text": parts[1]
                    })
            
            # page.selectOption()
            elif 'selectOption(' in line:
                parts = re.findall(r"['\"]([^'\"]+)['\"]", line)
                if len(parts) >= 1:
                    actions.append({
                        "type": "select",
                        "option": parts[-1]
                    })
        
        return actions
    
    def _extract_actions_from_python(self, code: str) -> List[Dict[str, Any]]:
        """Extrae acciones de código Python."""
        actions = []
        lines = code.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # page.goto()
            if 'page.goto(' in line:
                url_match = re.search(r"page\.goto\(['\"]([^'\"]+)['\"]", line)
                if url_match:
                    actions.append({
                        "type": "navigate",
                        "url": url_match.group(1)
                    })
            
            # Similar patterns for Python...
            elif 'page.click(' in line or '.click()' in line:
                selector_match = re.search(r"['\"]([^'\"]+)['\"]", line)
                if selector_match:
                    actions.append({
                        "type": "click",
                        "selector": selector_match.group(1)
                    })
        
        return actions
    
    def _extract_actions_generic(self, code: str) -> List[Dict[str, Any]]:
        """Extrae acciones de código de cualquier formato."""
        actions = []
        
        # Patrones genéricos para encontrar URLs
        url_pattern = r"https?://[^\s'\"]+|['\"]https?://[^'\"]+['\"]"
        urls = re.findall(url_pattern, code)
        
        if urls:
            # Limpiar URL
            url = urls[0].strip('\'"')
            actions.append({
                "type": "navigate",
                "url": url
            })
        
        # Patrones para clicks y fills...
        click_patterns = [
            r"click\(['\"]([^'\"]+)['\"]",
            r"getByRole\(['\"]([^'\"]+)['\"]",
            r"getByText\(['\"]([^'\"]+)['\"]"
        ]
        
        for pattern in click_patterns:
            matches = re.findall(pattern, code, re.IGNORECASE)
            for match in matches:
                actions.append({
                    "type": "click",
                    "selector": match
                })
        
        return actions
    
    def _actions_to_instructions(self, actions: List[Dict[str, Any]]) -> str:
        """Convierte acciones extraídas a instrucciones en lenguaje natural."""
        if not actions:
            return "Realiza las acciones de prueba según el código proporcionado."
        
        instructions = []
        instructions.append("Ejecuta las siguientes acciones paso a paso:")
        
        for i, action in enumerate(actions, 1):
            if action["type"] == "navigate":
                instructions.append(f"{i}. Navega a la URL: {action['url']}")
            elif action["type"] == "click":
                instructions.append(f"{i}. Haz clic en el elemento: {action['selector']}")
            elif action["type"] == "fill":
                instructions.append(f"{i}. Rellena el campo '{action['selector']}' con el texto: {action['text']}")
            elif action["type"] == "select":
                instructions.append(f"{i}. Selecciona la opción: {action['option']}")
        
        instructions.append("\nVerifica que cada acción se ejecute correctamente antes de continuar con la siguiente.")
        
        return "\n".join(instructions)
    
    def _analyze_execution_result(self, history) -> Dict[str, Any]:
        """Analiza el resultado de la ejecución."""
        try:
            # Análisis básico del historial
            # AgentHistoryList tiene un atributo .history que es una lista
            if hasattr(history, 'history'):
                history_items = history.history
                total_steps = len(history_items) if history_items else 0
            elif isinstance(history, list):
                history_items = history
                total_steps = len(history)
            else:
                # Si no podemos determinar el tipo, intentar convertir a string para análisis
                history_items = []
                total_steps = 1 if str(history) else 0
            
            success = total_steps > 0
            
            # Extraer pasos detallados para mostrar en el frontend
            steps = []
            for i, item in enumerate(history_items):
                try:
                    step_info = {
                        "step_number": i + 1,
                        "action": "Unknown",
                        "description": "No description available",
                        "status": "completed",
                        "timestamp": None
                    }
                    
                    # Intentar extraer información del paso
                    if hasattr(item, 'action'):
                        step_info["action"] = str(item.action)
                    elif isinstance(item, dict):
                        step_info["action"] = item.get('action', 'Unknown')
                        step_info["description"] = item.get('description', item.get('message', 'No description'))
                        step_info["status"] = item.get('status', 'completed')
                        
                    # Intentar extraer timestamp
                    if hasattr(item, 'timestamp'):
                        step_info["timestamp"] = item.timestamp
                    elif isinstance(item, dict) and 'timestamp' in item:
                        step_info["timestamp"] = item['timestamp']
                    
                    steps.append(step_info)
                    
                except Exception as e:
                    logger.warning(f"Error procesando paso {i}: {str(e)}")
                    steps.append({
                        "step_number": i + 1,
                        "action": "Error",
                        "description": f"Error procesando paso: {str(e)}",
                        "status": "error",
                        "timestamp": None
                    })
            
            return {
                "success": success,
                "total_steps": total_steps,
                "analysis": "Ejecución completada" if success else "No se ejecutaron pasos",
                "steps": steps,
                "details": {
                    "steps_completed": total_steps,
                    "execution_summary": f"Se ejecutaron {total_steps} pasos",
                    "history_type": str(type(history).__name__)
                }
            }
        except Exception as e:
            logger.error(f"Error analizando resultado de ejecución: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "analysis": "Error analizando resultado",
                "steps": [],
                "details": {}
            }
    
    def get_execution(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """Obtiene información de una ejecución."""
        return self.active_executions.get(execution_id)
    
    def list_executions(self) -> List[Dict[str, Any]]:
        """Lista todas las ejecuciones activas."""
        return [
            {
                "execution_id": exec_id,
                "session_id": exec_info["session_id"],
                "status": exec_info["status"],
                "created_at": exec_info["created_at"].isoformat(),
                "updated_at": exec_info["updated_at"].isoformat(),
                "target_url": exec_info.get("target_url")
            }
            for exec_id, exec_info in self.active_executions.items()
        ]
    
    async def stop_execution(self, execution_id: str) -> bool:
        """Detiene una ejecución activa."""
        if execution_id not in self.active_executions:
            return False
        
        execution = self.active_executions[execution_id]
        if execution["status"] in ["completed", "failed", "stopped"]:
            return True
        
        execution["status"] = "stopped"
        execution["updated_at"] = datetime.now()
        
        logger.info(f"Ejecución {execution_id} detenida")
        return True
    
    def _extract_screenshots_from_history(self, history, screenshots_dir: str) -> List[str]:
        """Extrae screenshots del historial de browser-use y los guarda en el directorio especificado.
        
        Args:
            history: Historial de ejecución de browser-use
            screenshots_dir: Directorio donde guardar los screenshots
            
        Returns:
            Lista de rutas de los screenshots guardados
        """
        screenshots = []
        
        try:
            # Browser-use puede tener screenshots en diferentes formatos
            # Vamos a intentar extraer de las diferentes formas posibles
            
            if hasattr(history, 'history'):
                history_items = history.history
            elif isinstance(history, list):
                history_items = history
            else:
                # Si no podemos acceder al historial, crear screenshot del estado final
                logger.warning("No se puede acceder al historial para extraer screenshots")
                return screenshots
            
            # Recorrer los items del historial
            for i, item in enumerate(history_items):
                try:
                    # Browser-use puede almacenar screenshots en diferentes campos
                    screenshot_data = None
                    
                    # Intentar diferentes formas de acceder a los screenshots
                    if hasattr(item, 'screenshot'):
                        screenshot_data = item.screenshot
                    elif isinstance(item, dict):
                        screenshot_data = item.get('screenshot') or item.get('image') or item.get('screenshot_data')
                    
                    if screenshot_data:
                        # Guardar el screenshot
                        screenshot_path = os.path.join(screenshots_dir, f"step_{i+1}.png")
                        
                        # Si es base64, decodificar y guardar
                        if isinstance(screenshot_data, str):
                            # Remover prefijo data:image si existe
                            if screenshot_data.startswith('data:image'):
                                screenshot_data = screenshot_data.split(',')[1]
                            
                            with open(screenshot_path, "wb") as f:
                                f.write(base64.b64decode(screenshot_data))
                        elif isinstance(screenshot_data, bytes):
                            with open(screenshot_path, "wb") as f:
                                f.write(screenshot_data)
                        else:
                            continue
                        
                        screenshots.append(screenshot_path)
                        logger.debug(f"Screenshot guardado: {screenshot_path}")
                        
                except Exception as e:
                    logger.warning(f"Error procesando screenshot {i}: {str(e)}")
                    continue
            
            # Si no encontramos screenshots en el historial, intentar crear uno del estado final
            if not screenshots:
                logger.info("No se encontraron screenshots en el historial, esto es normal para browser-use")
                # Browser-use no siempre genera screenshots automáticamente
                # Los screenshots son opcionales y dependen de la configuración
            
        except Exception as e:
            logger.error(f"Error extrayendo screenshots del historial: {str(e)}")
        
        return screenshots
