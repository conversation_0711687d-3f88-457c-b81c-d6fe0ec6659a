"""Servicio central para ejecución de pruebas, independiente de la interfaz."""

import os
from typing import Dict, Any, Optional, List
from datetime import datetime

from src.Utilities.test_executor import TestExecutor
from src.Utilities.project_manager_service import ProjectManagerService
from src.Utilities.project_manager import Project, TestSuite, TestCase, GitHubConfig

from src.Core.test_service_extensions import (
    TestServiceSuitesMixin,
    TestServiceTestCasesMixin,
    TestServiceExecutionMixin
)

class TestService(TestServiceSuitesMixin, TestServiceTestCasesMixin, TestServiceExecutionMixin):
    """Servicio para gestionar la ejecución de pruebas, generación de código y proyectos.

    Esta clase actúa como una capa de servicio que desacopla completamente la lógica
    de negocio de las interfaces de usuario (CLI, API web).
    """

    def __init__(self, api_key: str, language: Optional[str] = None):
        """Inicializa el servicio de pruebas.

        Args:
            api_key: API key para el LLM (Google Gemini)
            language: Idioma para los prompts ('en' o 'es')
        """
        self.api_key = api_key
        self.language = language or os.getenv("PROMPT_LANGUAGE", "en")
        self.test_executor = TestExecutor(api_key=api_key, language=self.language)
        self.project_manager = ProjectManagerService()

    def run_smoke_test(self, instructions: str, url: Optional[str] = None, user_story: Optional[str] = None,
                      config_id: Optional[str] = None, configuration: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Ejecuta un smoke test.

        Args:
            instructions: Instrucciones para el test
            url: URL para la prueba (opcional)
            user_story: Historia de usuario (opcional)
            config_id: ID de configuración predefinida o personalizada a usar (opcional)
            configuration: Configuración específica para la ejecución (opcional)

        Returns:
            Dict[str, Any]: Resultado de la ejecución
        """
        return self.test_executor.run_smoke_test(
            instructions=instructions,
            url=url,
            user_story=user_story,
            config_id=config_id,
            configuration=configuration
        )

    async def run_smoke_test_async(self, instructions: str, url: Optional[str] = None, user_story: Optional[str] = None,
                                  config_id: Optional[str] = None, configuration: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Ejecuta un smoke test de manera asíncrona.

        Args:
            instructions: Instrucciones para el test
            url: URL para la prueba (opcional)
            user_story: Historia de usuario (opcional)
            config_id: ID de configuración predefinida o personalizada a usar (opcional)
            configuration: Configuración específica para la ejecución (opcional)

        Returns:
            Dict[str, Any]: Resultado de la ejecución
        """
        try:
            # Crear el escenario Gherkin
            gherkin_scenario = self.test_executor.create_gherkin_scenario(
                instructions=instructions,
                url=url,
                user_story=user_story
            )

            # Ejecutar el test de manera asíncrona
            result = await self.test_executor.execute_smoke_test(
                gherkin_scenario=gherkin_scenario,
                url=url,
                config_id=config_id,
                configuration=configuration
            )
            return result

        except Exception as e:
            # Asegurar que cualquier excepción se maneje correctamente
            return {
                "success": False,
                "error": str(e),
                "test_id": None,
                "history": None,
                "screenshot_paths": [],
                "history_path": None
            }

    def run_full_test(self, gherkin_scenario: str, url: Optional[str] = None) -> Dict[str, Any]:
        """Ejecuta un test completo.

        Args:
            gherkin_scenario: Escenario Gherkin a ejecutar
            url: URL para la prueba (opcional)

        Returns:
            Dict[str, Any]: Resultado de la ejecución
        """
        return self.test_executor.run_full_test(
            gherkin_scenario=gherkin_scenario,
            url=url
        )

    def create_gherkin_scenario(self, instructions: str, url: Optional[str] = None, user_story: Optional[str] = None) -> str:
        """Crea un escenario Gherkin a partir de instrucciones.

        Args:
            instructions: Instrucciones para la prueba
            url: URL para la prueba (opcional)
            user_story: Historia de usuario (opcional)

        Returns:
            str: Escenario Gherkin generado
        """
        return self.test_executor.create_gherkin_scenario(
            instructions=instructions,
            url=url,
            user_story=user_story
        )

    def save_history_to_project(self, project_id: str, suite_id: str, test_history: Dict[str, Any],
                               name: str, description: str, gherkin: str) -> Dict[str, Any]:
        """Guarda un historial de prueba en un proyecto.

        Args:
            project_id: ID del proyecto
            suite_id: ID de la suite de pruebas
            test_history: Historial de la prueba
            name: Nombre del caso de prueba
            description: Descripción del caso de prueba
            gherkin: Escenario Gherkin

        Returns:
            Dict[str, Any]: Objeto del caso de prueba creado
        """
        # Crear un caso de prueba con la información de la prueba
        test_case = self.project_manager.create_test_case(
            project_id=project_id,
            suite_id=suite_id,
            name=name,
            description=description,
            gherkin=gherkin
        )

        # Actualizar el caso de prueba con el historial
        if test_case and "history_json_path" in test_history:
            test_case.history_files.append(test_history["history_json_path"])

            # Guardar el proyecto actualizado
            project = self.project_manager.get_project(project_id)
            if project:
                self.project_manager.save_project(project)

        return test_case

    def generate_code(self, framework: str, gherkin_scenario: str, test_history: Dict[str, Any]) -> str:
        """Genera código de automatización.

        Args:
            framework: Framework para generar el código (selenium, playwright, etc.)
            gherkin_scenario: Escenario Gherkin
            test_history: Historial de la prueba

        Returns:
            str: Código de automatización generado
        """
        from src.Core.prompt_service import PromptService
        from langchain_google_genai import ChatGoogleGenerativeAI
        import json

        # No longer needed - using PromptService

        # Preparar datos para la generación
        history_for_code = {
            "urls": test_history.get("urls", []),
            "action_names": test_history.get("action_names", []),
            "detailed_actions": test_history.get("detailed_actions", []),
            "element_xpaths": test_history.get("element_xpaths", {}),
            "extracted_content": test_history.get("extracted_content", []),
            "errors": test_history.get("errors", []),
            "model_actions": test_history.get("model_actions", []),
            "execution_date": test_history.get("execution_date", datetime.now().strftime("%d/%m/%Y %H:%M:%S")),
            "test_id": test_history.get("test_id", "unknown"),
            "screenshot_paths": test_history.get("screenshot_paths", [])
        }

        # Mapeo de frameworks a IDs de prompts
        framework_prompt_ids = {
            "selenium": "code_gen_selenium_pytest",
            "playwright": "code_gen_playwright",
            "cypress": "code_gen_cypress",
            "robot": "code_gen_robot",
            "java": "code_gen_java_selenium"
        }

        # Verificar que el framework solicitado existe
        if framework not in framework_prompt_ids:
            raise ValueError(f"Framework no soportado: {framework}")

        # Use the new prompt service
        prompt_service = PromptService()

        # Map framework names to prompt IDs
        framework_mapping = {
            "selenium": "selenium-pytest",
            "playwright": "playwright",
            "cypress": "cypress",
            "robot": "robot-framework",
            "java": "java-selenium"
        }

        if framework not in framework_mapping:
            raise ValueError(f"Framework no soportado: {framework}")

        prompt_id = framework_mapping[framework]

        # Generate code using the prompt service
        return prompt_service.generate_automation_code(
            framework=prompt_id,
            gherkin_scenario=gherkin_scenario,
            history=history_for_code,
            language=self.language
        )

    def summarize_test_results(self, test_results: str) -> str:
        """Genera un resumen de los resultados de prueba usando IA.

        Args:
            test_results: Resultados de la prueba a resumir

        Returns:
            str: Resumen generado por IA
        """
        from src.Core.prompt_service import PromptService
        from langchain_google_genai import ChatGoogleGenerativeAI

        try:
            # Use the new prompt service
            prompt_service = PromptService()
            return prompt_service.summarize_test_results(test_results, self.language)

        except Exception as e:
            # En caso de error, devolver un mensaje de error descriptivo
            return f"Error al generar el resumen: {str(e)}"

    # === GESTIÓN DE PROYECTOS ===

    def create_project(self, name: str, description: str = "", tags: List[str] = None, 
                      github_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Crea un nuevo proyecto.

        Args:
            name: Nombre del proyecto
            description: Descripción del proyecto
            tags: Lista de etiquetas
            github_config: Configuración de GitHub (opcional)

        Returns:
            Dict[str, Any]: Datos del proyecto creado
        """
        # Crear config de GitHub si se proporciona
        if github_config is not None:
            github_config_obj = GitHubConfig(**github_config)
        else:
            github_config_obj = None
            
        # Crear el proyecto
        project = self.project_manager.create_project(
            name=name, 
            description=description, 
            tags=tags or [],
            github_config=github_config_obj
        )
        
        # Si GitHub está habilitado, crear la suite de análisis de PR
        if github_config and github_config.get('enabled'):
            self.create_github_suite(project.project_id)
            
        return project.to_dict()

    def get_project(self, project_id: str) -> Optional[Dict[str, Any]]:
        """Obtiene un proyecto por su ID.

        Args:
            project_id: ID del proyecto

        Returns:
            Optional[Dict[str, Any]]: Datos del proyecto o None si no existe
        """
        project = self.project_manager.get_project(project_id)
        return project.to_dict() if project else None

    def get_all_projects(self) -> List[Dict[str, Any]]:
        """Obtiene todos los proyectos.

        Returns:
            List[Dict[str, Any]]: Lista de proyectos
        """
        projects = self.project_manager.get_all_projects()
        return [project.to_dict() for project in projects]

    def update_project(self, project_id: str, name: str = None, description: str = None,
                      tags: List[str] = None, github_config: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """Actualiza un proyecto existente.

        Args:
            project_id: ID del proyecto
            name: Nuevo nombre (opcional)
            description: Nueva descripción (opcional)
            tags: Nuevas etiquetas (opcional)
            github_config: Nueva configuración de GitHub (opcional)

        Returns:
            Optional[Dict[str, Any]]: Datos del proyecto actualizado o None si no existe
        """
        # Crear config de GitHub si se proporciona
        if github_config is not None:
            github_config_obj = GitHubConfig(**github_config)
        else:
            github_config_obj = None

        # Actualizar proyecto
        project = self.project_manager.update_project(
            project_id=project_id,
            name=name,
            description=description,
            tags=tags,
            github_config=github_config_obj
        )

        if not project:
            return None

        # Si se habilitó GitHub, crear la suite de análisis de PR
        if github_config and github_config.get('enabled'):
            self.create_github_suite(project_id)

        return project.to_dict()

    def delete_project(self, project_id: str) -> bool:
        """Elimina un proyecto.

        Args:
            project_id: ID del proyecto

        Returns:
            bool: True si se eliminó correctamente, False en caso contrario
        """
        return self.project_manager.delete_project(project_id)

    def create_github_suite(self, project_id: str) -> Optional[Dict[str, Any]]:
        """Crea una suite de pruebas para análisis de Pull Requests de GitHub.
        
        Args:
            project_id: ID del proyecto
            
        Returns:
            Optional[Dict[str, Any]]: Datos de la suite creada o None si el proyecto no existe
        """
        project = self.project_manager.get_project(project_id)
        if not project:
            return None

        # Crear suite de GitHub si no existe ya
        github_suite = next(
            (suite for suite in project.get_all_test_suites() if "github-pr-analysis" in suite.tags),
            None
        )

        if not github_suite:
            return self.create_test_suite(
                project_id=project_id,
                name="GitHub PR Analysis",
                description="Tests generated from GitHub Pull Request analysis",
                tags=["github-pr-analysis", "automated"]
            )
        
        return github_suite.to_dict()