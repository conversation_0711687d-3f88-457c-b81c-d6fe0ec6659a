import os
import json
import shutil
import base64
from datetime import datetime
from typing import List, Dict, Any, Optional, Union, Tuple
from .project_manager import Project, TestSuite, TestCase, GitHubConfig


def extract_screenshots_from_json(json_file_path):
    """
    Extrae las capturas de pantalla en base64 de un archivo JSON de historial y las devuelve como data URLs.
    Implementa manejo robusto de objetos problemáticos como DOMHistoryElement.

    Args:
        json_file_path: Ruta al archivo JSON de historial

    Returns:
        List[str]: Lista de data URLs (data:image/png;base64,...)
    """
    import json
    
    try:
        with open(json_file_path, "r") as f:
            history_data = json.load(f)

        # Extraer capturas de pantalla
        screenshot_data_urls = []

        # Buscar capturas en la estructura del historial usando método seguro
        try:
            for step in history_data.get("history", []):
                # Buscar en el estado con manejo de errores mejorado
                if "state" in step and isinstance(step["state"], dict) and "screenshot" in step["state"]:
                    screenshot = step["state"]["screenshot"]
                    if isinstance(screenshot, str) and len(screenshot) > 100:  # Probablemente es base64
                        try:
                            # Verificar si ya es una data URL
                            if screenshot.startswith("data:image"):
                                screenshot_data_urls.append(screenshot)
                            else:
                                # Es base64 puro, convertir a data URL
                                # Asumir que es PNG (la mayoría de las capturas de pantalla)
                                data_url = f"data:image/png;base64,{screenshot}"
                                screenshot_data_urls.append(data_url)
                        except Exception as e:
                            print(f"⚠️ Error al procesar captura de pantalla: {str(e)}")
        except Exception as e:
            print(f"⚠️ Error al procesar historial de pasos: {str(e)}")

        # Si no se encontraron capturas en el estado, buscar en model_actions
        if len(screenshot_data_urls) == 0:
            try:
                for action in history_data.get("model_actions", []):
                    if isinstance(action, dict) and "screenshot" in action:
                        screenshot = action["screenshot"]
                        if isinstance(screenshot, str) and len(screenshot) > 100:
                            try:
                                if screenshot.startswith("data:image"):
                                    screenshot_data_urls.append(screenshot)
                                else:
                                    data_url = f"data:image/png;base64,{screenshot}"
                                    screenshot_data_urls.append(data_url)
                            except Exception as e:
                                print(f"⚠️ Error al procesar captura de model_actions: {str(e)}")
            except Exception as e:
                print(f"⚠️ Error al procesar model_actions: {str(e)}")

        # Buscar en cualquier parte del JSON donde pueda haber una captura de pantalla usando método recursivo seguro
        if len(screenshot_data_urls) == 0:
            def extract_from_dict_safe(data, paths=None, prefix="", max_depth=10):
                """
                Extrae capturas de pantalla de manera recursiva con protección contra objetos problemáticos.
                """
                if paths is None:
                    paths = []
                    
                if max_depth <= 0:  # Prevenir recursión infinita
                    return paths

                try:
                    if isinstance(data, dict):
                        for key, value in data.items():
                            try:
                                if key == "screenshot" and isinstance(value, str) and len(value) > 100:
                                    paths.append((prefix + "." + key, value))
                                elif isinstance(value, (dict, list)) and max_depth > 0:
                                    extract_from_dict_safe(value, paths, prefix + "." + key if prefix else key, max_depth - 1)
                            except Exception as e:
                                # Skip problematic keys/values silently con más detalle en debug
                                if "DOMHistoryElement" in str(e):
                                    print(f"🔍 Skipping DOMHistoryElement object at {prefix}.{key}")
                                continue
                    elif isinstance(data, list):
                        for i, item in enumerate(data):
                            try:
                                # Solo procesar tipos serializables básicos y limitar profundidad
                                if isinstance(item, (dict, list)) and max_depth > 0:
                                    extract_from_dict_safe(item, paths, f"{prefix}[{i}]", max_depth - 1)
                                elif isinstance(item, str) and len(item) > 100 and prefix.endswith("screenshot"):
                                    # Si es un string largo en un contexto de screenshot, podría ser base64
                                    paths.append((f"{prefix}[{i}]", item))
                            except Exception as e:
                                # Skip problematic items silently con más detalle en debug
                                if "DOMHistoryElement" in str(e):
                                    print(f"🔍 Skipping DOMHistoryElement object at {prefix}[{i}]")
                                continue
                except Exception as e:
                    print(f"⚠️ Error al procesar estructura de datos en {prefix}: {str(e)}")

                return paths

            # Extraer todas las capturas de pantalla del JSON con protección mejorada
            try:
                screenshot_entries = extract_from_dict_safe(history_data)

                # Procesar cada captura encontrada
                for i, (path, screenshot) in enumerate(screenshot_entries):
                    try:
                        if isinstance(screenshot, str) and len(screenshot) > 100:
                            if screenshot.startswith("data:image"):
                                screenshot_data_urls.append(screenshot)
                            else:
                                # Es base64 puro, convertir a data URL
                                data_url = f"data:image/png;base64,{screenshot}"
                                screenshot_data_urls.append(data_url)
                    except Exception as e:
                        print(f"⚠️ Error al procesar captura de pantalla {i+1}: {str(e)}")
            except Exception as e:
                print(f"⚠️ Error al extraer capturas del JSON: {str(e)}")

        print(f"📸 Extraídas {len(screenshot_data_urls)} capturas de pantalla desde {json_file_path}")
        return screenshot_data_urls
        
    except json.JSONDecodeError as e:
        print(f"❌ Error al decodificar JSON: {str(e)}")
        return []
    except Exception as e:
        print(f"❌ Error al extraer capturas de pantalla: {str(e)}")
        return []

def load_test_history(json_file_path):
    """
    Carga y procesa un archivo JSON de historial de pruebas para su visualización.

    Args:
        json_file_path: Ruta al archivo JSON de historial

    Returns:
        dict: Datos procesados del historial de pruebas
    """
    import json
    
    try:
        with open(json_file_path, "r") as f:
            history_data = json.load(f)

        # Verificar si el historial está vacío
        if not history_data.get("history") or len(history_data.get("history", [])) == 0:
            return {
                "actions": [],
                "results": [],
                "elements": [],
                "urls": [],
                "errors": ["El historial de ejecución está vacío. La prueba no se completó correctamente."],
                "screenshots": [],
                "metadata": {
                    "start_time": None,
                    "end_time": None,
                    "total_steps": 0,
                    "success": False,
                    "empty_history": True,
                    "file_path": json_file_path
                }
            }

        # Procesar los datos para visualización
        processed_data = {
            "actions": [],
            "results": [],
            "elements": [],
            "urls": [],
            "errors": [],
            "screenshots": [],
            "metadata": {
                "start_time": None,
                "end_time": None,
                "total_steps": 0,
                "success": False,
                "empty_history": False,
                "file_path": json_file_path
            }
        }

        # Extraer información de cada paso
        for step in history_data.get("history", []):
            # Extraer acciones
            if "model_output" in step and step["model_output"] is not None and "action" in step["model_output"]:
                for action in step["model_output"]["action"]:
                    try:
                        # Limpiar la acción para evitar problemas de serialización
                        if isinstance(action, dict):
                            # Crear una copia limpia de la acción
                            clean_action = {}
                            for key, value in action.items():
                                try:
                                    # Solo incluir valores serializables
                                    if isinstance(value, (str, int, float, bool, type(None))):
                                        clean_action[key] = value
                                    elif isinstance(value, (dict, list)):
                                        # Para diccionarios y listas, intentar serializar
                                        try:
                                            json.dumps(value)  # Test serialization
                                            clean_action[key] = value
                                        except (TypeError, ValueError):
                                            # Si no se puede serializar, convertir a string
                                            clean_action[key] = str(value)
                                    else:
                                        # Convertir objetos complejos a string
                                        clean_action[key] = str(value)
                                except Exception:
                                    # Si hay error, convertir a string
                                    clean_action[key] = str(value)
                            
                            processed_data["actions"].append({
                                "type": list(clean_action.keys())[0] if clean_action else "unknown",
                                "details": clean_action,
                                "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                            })
                        else:
                            # Si la acción no es un diccionario, convertir a string
                            processed_data["actions"].append({
                                "type": "unknown",
                                "details": {"raw_action": str(action)},
                                "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                            })
                    except Exception as e:
                        print(f"Error al procesar acción en paso {step.get('metadata', {}).get('step_number', 0)}: {str(e)}")
                        # En caso de error, agregar una acción básica
                        processed_data["actions"].append({
                            "type": "error",
                            "details": {"error": f"Error al procesar acción: {str(e)}"},
                            "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                        })

            # Extraer resultados
            if "result" in step:
                for result in step["result"]:
                    try:
                        if "extracted_content" in result:
                            # Limpiar el contenido extraído para evitar problemas de serialización
                            extracted_content = result["extracted_content"]
                            
                            # Si es un objeto complejo, convertir a string
                            if not isinstance(extracted_content, (str, int, float, bool, type(None), dict, list)):
                                extracted_content = str(extracted_content)
                            
                            processed_data["results"].append({
                                "content": extracted_content,
                                "is_done": result.get("is_done", False),
                                "success": result.get("success", False),
                                "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                            })
                    except Exception as e:
                        print(f"Error al procesar resultado en paso {step.get('metadata', {}).get('step_number', 0)}: {str(e)}")
                        # En caso de error, agregar un resultado básico
                        processed_data["results"].append({
                            "content": f"Error al procesar resultado: {str(e)}",
                            "is_done": False,
                            "success": False,
                            "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                        })

            # Extraer elementos interactuados
            if "state" in step and step["state"] is not None and "interacted_element" in step["state"]:
                try:
                    for element in step["state"]["interacted_element"]:
                        if element:
                            # Limpiar elemento para evitar problemas de serialización
                            try:
                                # Si el elemento es un objeto complejo, extraer solo datos serializables
                                if hasattr(element, '__dict__') or hasattr(element, '__class__'):
                                    # Es un objeto, convertir a diccionario básico con manejo seguro
                                    element_dict = {}
                                    try:
                                        if hasattr(element, 'tag_name'):
                                            element_dict["tag_name"] = str(element.tag_name) if element.tag_name else ""
                                        else:
                                            element_dict["tag_name"] = ""
                                    except:
                                        element_dict["tag_name"] = ""
                                    
                                    try:
                                        if hasattr(element, 'xpath'):
                                            element_dict["xpath"] = str(element.xpath) if element.xpath else ""
                                        else:
                                            element_dict["xpath"] = ""
                                    except:
                                        element_dict["xpath"] = ""
                                    
                                    try:
                                        if hasattr(element, 'attributes'):
                                            element_dict["attributes"] = dict(element.attributes) if element.attributes else {}
                                        else:
                                            element_dict["attributes"] = {}
                                    except:
                                        element_dict["attributes"] = {}
                                    
                                    processed_data["elements"].append({
                                        **element_dict,
                                        "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                                    })
                                elif isinstance(element, dict):
                                    # Es un diccionario, procesar normalmente
                                    processed_data["elements"].append({
                                        "tag_name": element.get("tag_name", ""),
                                        "xpath": element.get("xpath", ""),
                                        "attributes": element.get("attributes", {}),
                                        "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                                    })
                                else:
                                    # Tipo no reconocido, convertir a string de manera segura
                                    try:
                                        element_str = str(element)
                                        processed_data["elements"].append({
                                            "tag_name": "",
                                            "xpath": "",
                                            "attributes": {},
                                            "raw_data": element_str,
                                            "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                                        })
                                    except:
                                        # Si hasta str() falla, usar un marcador de posición
                                        processed_data["elements"].append({
                                            "tag_name": "unknown",
                                            "xpath": "",
                                            "attributes": {},
                                            "raw_data": "Could not serialize element",
                                            "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                                        })
                            except Exception as e:
                                print(f"Error al procesar elemento en paso {step.get('metadata', {}).get('step_number', 0)}: {str(e)}")
                                # En caso de error, agregar un elemento básico
                                processed_data["elements"].append({
                                    "tag_name": "",
                                    "xpath": "",
                                    "attributes": {},
                                    "error": f"Error al procesar elemento: {str(e)}",
                                    "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                                })
                except Exception as e:
                    print(f"Error al procesar lista de elementos en paso {step.get('metadata', {}).get('step_number', 0)}: {str(e)}")
                    # En caso de error, agregar un elemento básico
                    processed_data["elements"].append({
                        "tag_name": "",
                        "xpath": "",
                        "attributes": {},
                        "raw_data": f"Error: {str(e)}",
                        "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                    })

            # Extraer URLs
            if "state" in step and step["state"] is not None and "url" in step["state"]:
                url = step["state"]["url"]
                if url and url != "about:blank":
                    processed_data["urls"].append({
                        "url": url,
                        "title": step["state"].get("title", ""),
                        "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                    })

            # Extraer metadatos
            if "metadata" in step:
                if processed_data["metadata"]["start_time"] is None or step["metadata"].get("step_start_time", 0) < processed_data["metadata"]["start_time"]:
                    processed_data["metadata"]["start_time"] = step["metadata"].get("step_start_time", 0)

                if processed_data["metadata"]["end_time"] is None or step["metadata"].get("step_end_time", 0) > processed_data["metadata"]["end_time"]:
                    processed_data["metadata"]["end_time"] = step["metadata"].get("step_end_time", 0)

                processed_data["metadata"]["total_steps"] = max(processed_data["metadata"]["total_steps"], step["metadata"].get("step_number", 0))

        # Verificar si la prueba fue exitosa
        last_results = [r for r in processed_data["results"] if r.get("is_done", False)]
        if last_results:
            processed_data["metadata"]["success"] = last_results[-1].get("success", False)

        # Extraer y procesar capturas de pantalla
        screenshot_data_urls = extract_screenshots_from_json(json_file_path)
        processed_data["screenshots"] = screenshot_data_urls

        return processed_data

    except FileNotFoundError:
        print(f"Error: No se pudo encontrar el archivo {json_file_path}")
        return {
            "actions": [],
            "results": [],
            "elements": [],
            "urls": [],
            "errors": [f"Archivo de historial no encontrado: {json_file_path}"],
            "screenshots": [],
            "metadata": {
                "start_time": None,
                "end_time": None,
                "total_steps": 0,
                "success": False,
                "file_not_found": True,
                "file_path": json_file_path
            }
        }
    except json.JSONDecodeError as e:
        print(f"Error al decodificar JSON: {str(e)}")
        return {
            "actions": [],
            "results": [],
            "elements": [],
            "urls": [],
            "errors": [f"Error al leer el archivo de historial: {str(e)}"],
            "screenshots": [],
            "metadata": {
                "start_time": None,
                "end_time": None,
                "total_steps": 0,
                "success": False,
                "json_error": True,
                "file_path": json_file_path
            }
        }
    except Exception as e:
        print(f"Error inesperado al cargar historial: {str(e)}")
        return {
            "actions": [],
            "results": [],
            "elements": [],
            "urls": [],
            "errors": [f"Error inesperado al cargar historial: {str(e)}"],
            "screenshots": [],
            "metadata": {
                "start_time": None,
                "end_time": None,
                "total_steps": 0,
                "success": False,
                "unexpected_error": True,
                "file_path": json_file_path
            }
        }


class ProjectManagerService:
    """
    Servicio para gestionar proyectos, suites de pruebas y casos de prueba.
    """
    def __init__(self, base_dir: str = "projects"):
        self.base_dir = base_dir
        self.projects_dir = os.path.join(base_dir, "projects")
        self.ensure_directories()
        self.projects = self.load_projects()

    def ensure_directories(self) -> None:
        """Asegura que existan los directorios necesarios."""
        os.makedirs(self.base_dir, exist_ok=True)
        os.makedirs(self.projects_dir, exist_ok=True)

    def load_projects(self) -> Dict[str, Project]:
        """Carga todos los proyectos desde el sistema de archivos."""
        projects = {}

        if not os.path.exists(self.projects_dir):
            return projects

        for project_file in os.listdir(self.projects_dir):
            if project_file.endswith(".json"):
                project_path = os.path.join(self.projects_dir, project_file)
                try:
                    with open(project_path, "r") as f:
                        project_data = json.load(f)
                        project = Project.from_dict(project_data)
                        projects[project.project_id] = project
                except Exception as e:
                    print(f"Error al cargar el proyecto {project_file}: {str(e)}")

        return projects

    def save_project(self, project: Project) -> bool:
        """Guarda un proyecto en el sistema de archivos."""
        try:
            project_path = os.path.join(self.projects_dir, f"{project.project_id}.json")
            with open(project_path, "w") as f:
                json.dump(project.to_dict(), f, indent=2)
            return True
        except Exception as e:
            print(f"Error al guardar el proyecto {project.name}: {str(e)}")
            return False

    def create_project(self, name: str, description: str = "", tags: List[str] = None,
                      github_config: Optional[GitHubConfig] = None) -> Project:
        """Crea un nuevo proyecto.
        
        Args:
            name: Nombre del proyecto
            description: Descripción del proyecto
            tags: Lista de etiquetas
            github_config: Configuración de GitHub

        Returns:
            Project: Proyecto creado
        """
        project = Project(name=name, description=description, tags=tags, github_config=github_config)
        self.projects[project.project_id] = project
        self.save_project(project)
        return project

    def get_project(self, project_id: str) -> Optional[Project]:
        """Obtiene un proyecto por su ID."""
        return self.projects.get(project_id)

    def get_all_projects(self) -> List[Project]:
        """Obtiene todos los proyectos."""
        return list(self.projects.values())

    def update_project(self, project_id: str, name: str = None, description: str = None, 
                      tags: List[str] = None, github_config: Optional[GitHubConfig] = None) -> Optional[Project]:
        """Actualiza un proyecto existente."""
        project = self.get_project(project_id)
        if not project:
            return None

        if name is not None:
            project.name = name
        if description is not None:
            project.description = description
        if tags is not None:
            project.tags = tags
        if github_config is not None:
            project.github_config = github_config

        project.updated_at = datetime.now().isoformat()
        self.save_project(project)
        return project

    def delete_project(self, project_id: str) -> bool:
        """Elimina un proyecto."""
        if project_id not in self.projects:
            return False

        project_path = os.path.join(self.projects_dir, f"{project_id}.json")
        if os.path.exists(project_path):
            os.remove(project_path)

        del self.projects[project_id]
        return True

    def create_test_suite(self, project_id: str, name: str, description: str = "", tags: List[str] = None) -> Optional[TestSuite]:
        """Crea una nueva suite de pruebas en un proyecto."""
        project = self.get_project(project_id)
        if not project:
            return None

        test_suite = TestSuite(name=name, description=description, tags=tags)
        project.add_test_suite(test_suite)
        self.save_project(project)
        return test_suite

    def get_test_suite(self, project_id: str, suite_id: str) -> Optional[TestSuite]:
        """Obtiene una suite de pruebas por su ID."""
        project = self.get_project(project_id)
        if not project:
            return None

        return project.get_test_suite(suite_id)

    def update_test_suite(self, project_id: str, suite_id: str, name: str = None, description: str = None, tags: List[str] = None) -> Optional[TestSuite]:
        """Actualiza una suite de pruebas existente."""
        test_suite = self.get_test_suite(project_id, suite_id)
        if not test_suite:
            return None

        if name is not None:
            test_suite.name = name
        if description is not None:
            test_suite.description = description
        if tags is not None:
            test_suite.tags = tags

        test_suite.updated_at = datetime.now().isoformat()
        self.save_project(self.projects[project_id])
        return test_suite

    def delete_test_suite(self, project_id: str, suite_id: str) -> bool:
        """Elimina una suite de pruebas."""
        project = self.get_project(project_id)
        if not project:
            return False

        result = project.remove_test_suite(suite_id)
        if result:
            self.save_project(project)
        return result

    def create_test_case(self, project_id: str, suite_id: str, name: str, description: str = "",
                         instrucciones: str = "", historia_de_usuario: str = "",
                         gherkin: str = "", url: str = "", tags: List[str] = None) -> Optional[TestCase]:
        """Crea un nuevo caso de prueba en una suite."""
        test_suite = self.get_test_suite(project_id, suite_id)
        if not test_suite:
            return None

        test_case = TestCase(
            name=name,
            description=description,
            instrucciones=instrucciones,
            historia_de_usuario=historia_de_usuario,
            gherkin=gherkin,
            url=url,
            tags=tags
        )
        test_suite.add_test_case(test_case)
        self.save_project(self.projects[project_id])
        return test_case

    def get_test_case(self, project_id: str, suite_id: str, test_id: str) -> Optional[TestCase]:
        """Obtiene un caso de prueba por su ID."""
        test_suite = self.get_test_suite(project_id, suite_id)
        if not test_suite:
            return None

        return test_suite.get_test_case(test_id)

    def update_test_case(self, project_id: str, suite_id: str, test_id: str,
                         name: str = None, description: str = None,
                         instrucciones: str = None, historia_de_usuario: str = None,
                         gherkin: str = None, url: str = None, tags: List[str] = None) -> Optional[TestCase]:
        """Actualiza un caso de prueba existente."""
        test_case = self.get_test_case(project_id, suite_id, test_id)
        if not test_case:
            return None

        if name is not None:
            test_case.name = name
        if description is not None:
            test_case.description = description
        if instrucciones is not None:
            test_case.instrucciones = instrucciones
        if historia_de_usuario is not None:
            test_case.historia_de_usuario = historia_de_usuario
        if gherkin is not None:
            test_case.gherkin = gherkin
        if url is not None:
            test_case.url = url
        if tags is not None:
            test_case.tags = tags

        test_case.updated_at = datetime.now().isoformat()
        self.save_project(self.projects[project_id])
        return test_case

    def update_test_case_status(self, project_id: str, suite_id: str, test_id: str, status: str) -> Optional[TestCase]:
        """Actualiza el estado de un caso de prueba."""
        test_case = self.get_test_case(project_id, suite_id, test_id)
        if not test_case:
            return None

        test_case.status = status
        test_case.last_execution = datetime.now().isoformat()
        test_case.updated_at = datetime.now().isoformat()
        self.save_project(self.projects[project_id])
        return test_case

    def add_history_to_test_case(self, project_id: str, suite_id: str, test_id: str, history_path: str) -> Optional[TestCase]:
        """Agrega un archivo de historial a un caso de prueba."""
        test_case = self.get_test_case(project_id, suite_id, test_id)
        if not test_case:
            return None

        # Agregar el archivo de historial si no existe ya
        if history_path not in test_case.history_files:
            test_case.history_files.append(history_path)
            test_case.updated_at = datetime.now().isoformat()
            self.save_project(self.projects[project_id])

        return test_case

    def delete_test_case(self, project_id: str, suite_id: str, test_id: str) -> bool:
        """Elimina un caso de prueba."""
        test_suite = self.get_test_suite(project_id, suite_id)
        if not test_suite:
            return False

        result = test_suite.remove_test_case(test_id)
        if result:
            self.save_project(self.projects[project_id])
        return result


