{"94fc9cad-c838-41fe-b3be-e4663ff8f6be": {"session_id": "94fc9cad-c838-41fe-b3be-e4663ff8f6be", "status": "stopped", "target_language": "javascript", "url": "https://web-agent-playground.lovable.app/", "created_at": "2025-06-15T03:19:16.271342", "updated_at": "2025-06-15T03:20:44.440895", "completed_at": "2025-06-15T03:20:44.439343", "command_used": "npx playwright codegen -o /var/folders/97/l8phkxyx0l17r010dp87084h0000gn/T/qak_codegen/94fc9cad-c838-41fe-b3be-e4663ff8f6be/test.spec.js https://web-agent-playground.lovable.app/", "error_message": null, "generated_code": "import { test, expect } from '@playwright/test';\n\ntest('test', async ({ page }) => {\n  await page.goto('https://web-agent-playground.lovable.app/');\n  await page.getByRole('textbox', { name: '<PERSON><PERSON>' }).click();\n  await page.getByRole('textbox', { name: '<PERSON><PERSON>' }).fill('<EMAIL>');\n  await page.getByRole('textbox', { name: 'Email' }).press('Tab');\n  await page.getByRole('textbox', { name: '<PERSON>trase<PERSON>' }).fill('admin123');\n  await page.getByRole('textbox', { name: '<PERSON>trase<PERSON>' }).press('Enter');\n});", "project_integration": null, "artifacts_path": "/var/folders/97/l8phkxyx0l17r010dp87084h0000gn/T/qak_codegen/94fc9cad-c838-41fe-b3be-e4663ff8f6be"}, "80856ac5-8bf2-497c-8386-4716e34f1f45": {"session_id": "80856ac5-8bf2-497c-8386-4716e34f1f45", "status": "stopped", "target_language": "javascript", "url": "https://web-agent-playground.lovable.app/", "created_at": "2025-06-15T03:35:11.019327", "updated_at": "2025-06-15T03:35:33.503505", "completed_at": "2025-06-15T03:35:33.502278", "command_used": "npx playwright codegen -o /var/folders/97/l8phkxyx0l17r010dp87084h0000gn/T/qak_codegen/80856ac5-8bf2-497c-8386-4716e34f1f45/test.spec.js https://web-agent-playground.lovable.app/", "error_message": null, "generated_code": "import { test, expect } from '@playwright/test';\n\ntest('test', async ({ page }) => {\n  await page.goto('https://web-agent-playground.lovable.app/');\n  await page.getByRole('textbox', { name: '<PERSON><PERSON>' }).click();\n  await page.getByRole('textbox', { name: '<PERSON><PERSON>' }).fill('<EMAIL>');\n  await page.getByRole('textbox', { name: 'Email' }).press('Tab');\n  await page.getByRole('textbox', { name: '<PERSON><PERSON>e<PERSON>' }).fill('admin123');\n  await page.getByRole('button', { name: 'Iniciar <PERSON>' }).click();\n});", "project_integration": null, "artifacts_path": "/var/folders/97/l8phkxyx0l17r010dp87084h0000gn/T/qak_codegen/80856ac5-8bf2-497c-8386-4716e34f1f45"}, "995034fe-b5f7-44e8-b1bc-34ec3533b43a": {"session_id": "995034fe-b5f7-44e8-b1bc-34ec3533b43a", "status": "stopped", "target_language": "javascript", "url": "https://web-agent-playground.lovable.app/", "created_at": "2025-06-15T03:47:44.100091", "updated_at": "2025-06-15T03:48:08.057466", "completed_at": "2025-06-15T03:48:08.056070", "command_used": "npx playwright codegen -o /var/folders/97/l8phkxyx0l17r010dp87084h0000gn/T/qak_codegen/995034fe-b5f7-44e8-b1bc-34ec3533b43a/test.spec.js https://web-agent-playground.lovable.app/", "error_message": null, "generated_code": "import { test, expect } from '@playwright/test';\n\ntest('test', async ({ page }) => {\n  await page.goto('https://web-agent-playground.lovable.app/');\n  await page.getByRole('textbox', { name: '<PERSON><PERSON>' }).click();\n  await page.getByRole('textbox', { name: '<PERSON><PERSON>' }).fill('<EMAIL>');\n  await page.getByRole('textbox', { name: 'Email' }).press('Tab');\n  await page.getByRole('textbox', { name: '<PERSON>trase<PERSON>' }).fill('admin123');\n  await page.getByRole('textbox', { name: '<PERSON>tras<PERSON><PERSON>' }).press('Enter');\n  await page.getByRole('button', { name: 'Iniciar <PERSON>' }).click();\n  await page.getByRole('button', { name: '<PERSON>uario<PERSON>' }).click();\n});", "project_integration": null, "artifacts_path": "/var/folders/97/l8phkxyx0l17r010dp87084h0000gn/T/qak_codegen/995034fe-b5f7-44e8-b1bc-34ec3533b43a"}, "4d81aff0-c805-47a3-924e-2fe72eb9787c": {"session_id": "4d81aff0-c805-47a3-924e-2fe72eb9787c", "status": "stopped", "target_language": "javascript", "url": "https://web-agent-playground.lovable.app/", "created_at": "2025-06-15T03:50:31.500631", "updated_at": "2025-06-15T03:50:54.412071", "completed_at": "2025-06-15T03:50:54.410776", "command_used": "npx playwright codegen -o /var/folders/97/l8phkxyx0l17r010dp87084h0000gn/T/qak_codegen/4d81aff0-c805-47a3-924e-2fe72eb9787c/test.spec.js https://web-agent-playground.lovable.app/", "error_message": null, "generated_code": "import { test, expect } from '@playwright/test';\n\ntest('test', async ({ page }) => {\n  await page.goto('https://web-agent-playground.lovable.app/');\n  await page.getByRole('textbox', { name: '<PERSON><PERSON>' }).click();\n  await page.getByRole('textbox', { name: '<PERSON><PERSON>' }).fill('<EMAIL>');\n  await page.getByRole('textbox', { name: 'Email' }).press('Tab');\n  await page.getByRole('textbox', { name: '<PERSON>trase<PERSON>' }).fill('admin123');\n  await page.getByRole('textbox', { name: '<PERSON>trase<PERSON>' }).press('Enter');\n  await page.getByRole('button', { name: 'Iniciar <PERSON>' }).click();\n  await page.getByRole('button', { name: '<PERSON>uario<PERSON>' }).click();\n  await page.getByRole('button', { name: 'Reportes' }).click();\n});", "project_integration": null, "artifacts_path": "/var/folders/97/l8phkxyx0l17r010dp87084h0000gn/T/qak_codegen/4d81aff0-c805-47a3-924e-2fe72eb9787c"}}