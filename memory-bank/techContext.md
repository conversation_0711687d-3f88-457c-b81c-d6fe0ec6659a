# Tech Context: AgentQA - Automatización de Pruebas con IA

## Technologies Used

### **Backend (Python)**
*   **Core Framework:** FastAPI - API REST moderna y asíncrona
*   **Web Server:** Uvicorn - Servidor ASGI de alto rendimiento
*   **Browser Automation:** browser-use==0.2.5 - Automatización web con IA
*   **AI/LLM Frameworks:**
    *   LangChain - Framework para aplicaciones con LLM
    *   langchain-google-genai - Integración con Gemini
    *   langchain-openai - Integración con OpenAI
    *   langchain-anthropic - Integración con Claude
    *   langchain-groq - Integración con Groq
    *   agno - Framework adicional de IA

### **Frontend (TypeScript/React)**
*   **Framework:** Next.js 15+ con App Router
*   **UI Framework:** React 18+ con TypeScript
*   **Styling:** Tailwind CSS + shadcn/ui components
*   **State Management:** TanStack Query (React Query)
*   **Form Management:** React Hook Form + Zod validation
*   **UI Components:**
    *   Radix UI primitives (@radix-ui/react-*)
    *   Lucide React icons
    *   Custom shadcn/ui components
*   **Features Implemented:**
    *   Complete dashboard with sidebar navigation
    *   Project management interface
    *   QA Assistant with AI-powered tools
    *   Smoke Test Playground
    *   Browser Configuration Manager
    *   Prompts management with ES↔EN translation

### **Data Processing & Utilities**
*   **Data Science:** NumPy, Pandas
*   **CLI Interface:** Custom CLI con Python
*   **Formatting:** Tabulate para salida de datos

### **Development Tools**
*   **Environment:** Python-dotenv para configuración
*   **API Documentation:** FastAPI auto-genera Swagger/OpenAPI
*   **Type Safety:** Pydantic para validación de datos
*   **Package Management:** npm/yarn para frontend, pip para backend

## Development Setup

### **Backend Requirements**
```bash
# Instalar dependencias Python
pip install -r requirements.txt

# Variables de entorno requeridas
GOOGLE_API_KEY=your_gemini_api_key  # Principal
OPENAI_API_KEY=your_openai_key     # Opcional
ANTHROPIC_API_KEY=your_claude_key  # Opcional
GROQ_API_KEY=your_groq_key        # Opcional

# Configuración del servidor
API_HOST=0.0.0.0                  # Por defecto
API_PORT=8000                     # Por defecto
```

### **Frontend Requirements**
```bash
# Navegar al directorio web
cd web/
 
# Instalar dependencias
npm install

# Variables de entorno
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000/api

# Desarrollo
npm run dev  # Puerto 9002 con Turbopack

# Build para producción
npm run build
npm start
```

### **Project Structure**
```
AgentQA/
├── src/                      # Backend Python
│   ├── API/                  # FastAPI routes modulares
│   ├── Agents/              # Agentes de IA
│   ├── Config/              # Configuraciones
│   ├── Core/                # Funcionalidades core
│   ├── Utilities/           # Utilidades y servicios
│   └── Observability/       # Monitoreo y logs
├── web/                     # Frontend Next.js (PRINCIPAL)
│   ├── src/
│   │   ├── app/             # App Router pages
│   │   ├── components/      # React components
│   │   ├── lib/             # Utilities y API clients
│   │   └── types/           # TypeScript definitions
│   ├── public/              # Static assets
│   └── package.json         # Dependencies y scripts
├── prompts/                 # Templates de prompts
├── projects/                # Datos de proyectos
├── tests/                   # Historial de ejecuciones
├── memory-bank/             # Contexto para Copilot
└── browseruse-Docs/         # Documentación browser-use
```

## Technical Constraints

### **AI Model Dependencies**
*   **Primary:** Google Gemini (requires GOOGLE_API_KEY)
*   **Fallbacks:** OpenAI GPT, Anthropic Claude, Groq
*   **Limitation:** Sin API keys, funcionalidad limitada

### **Browser Automation Constraints**
*   **browser-use v0.2.5:** Versión específica para compatibilidad
*   **Headless Mode:** Configurable, pero requerido para producción
*   **Vision Capabilities:** Depende del modelo de IA seleccionado
*   **Network Requirements:** Acceso a internet para ejecución de pruebas

### **Performance Considerations**
*   **Concurrent Execution:** FastAPI soporta operaciones asíncronas
*   **Memory Usage:** Ejecuciones de navegador pueden ser intensivas
*   **API Rate Limits:** Limitado por quotas de proveedores de IA
*   **Storage:** Historial de pruebas incluye screenshots (espacio)

### **Security Constraints**
*   **API Keys:** Manejo seguro de credenciales de IA
*   **CORS:** Configurado para desarrollo local
*   **File System:** Acceso controlado para lectura/escritura de proyectos
*   **Browser Security:** Opción para deshabilitar seguridad en pruebas

### **Platform Compatibility**
*   **Backend:** Cross-platform Python 3.8+
*   **Frontend:** Navegadores modernos con soporte ES2017+
*   **Browser Automation:** Depende de Chromium/Chrome disponible
*   **CLI:** Compatible con shells Unix-like y Windows

### **Development Constraints**
*   **TypeScript:** Configurado con strict mode
*   **ESLint/Build:** Errores ignorados para desarrollo rápido
*   **Hot Reload:** Turbopack habilitado para desarrollo
*   **API Documentation:** Auto-generada, accesible en /docs

### **Internationalization**
*   **Backend:** Inglés para ejecución (browser-use compatibility)
*   **Frontend:** Soporte español/inglés
*   **Translation API:** OpenAI para traducción automática
*   **User Interface:** Idioma configurable por usuario
